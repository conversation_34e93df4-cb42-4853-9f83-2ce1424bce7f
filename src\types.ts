// أنواع الألوان
export interface Color {
  id: string
  hex: string
  rgb: {
    r: number
    g: number
    b: number
  }
  hsl: {
    h: number
    s: number
    l: number
  }
  name?: string
}

// أنواع لوحات الألوان
export interface ColorPalette {
  id: string
  name: string
  colors: Color[]
  createdAt: Date
  type: 'generated' | 'extracted' | 'manual'
  sourceImage?: string
  harmony?: ColorHarmony
}

// أنواع التناغم اللوني
export type ColorHarmony = 
  | 'monochromatic'
  | 'analogous'
  | 'complementary'
  | 'triadic'
  | 'tetradic'
  | 'splitComplementary'

// ثيم التطبيق
export type AppTheme = 'light' | 'dark'

// تبويبات التطبيق
export type AppTab = 'generator' | 'extractor' | 'favorites'

// إعدادات التصدير
export interface ExportSettings {
  format: 'ase' | 'svg' | 'json' | 'png' | 'txt'
  includeNames: boolean
  includeHex: boolean
  includeRgb: boolean
  includeHsl: boolean
}

// مجموعة المفضلة
export interface FavoriteGroup {
  id: string
  name: string
  palettes: ColorPalette[]
  createdAt: Date
}

// إعدادات مولد الألوان
export interface GeneratorSettings {
  baseColor: string
  harmony: ColorHarmony
  colorCount: number
  saturation: number
  lightness: number
}

// إعدادات استخراج الألوان
export interface ExtractorSettings {
  colorCount: number
  quality: number
  ignoreWhite: boolean
  ignoreBlack: boolean
}

// حالة التطبيق
export interface AppState {
  theme: AppTheme
  activeTab: AppTab
  currentPalette: ColorPalette | null
  savedPalettes: ColorPalette[]
  favoriteGroups: FavoriteGroup[]
  generatorSettings: GeneratorSettings
  extractorSettings: ExtractorSettings
}
