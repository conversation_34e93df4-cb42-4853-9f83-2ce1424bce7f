import React, { useState, useEffect } from 'react'
import { <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>, Save, Settings } from 'lucide-react'
import Sidebar from './components/Sidebar'
import MainContent from './components/MainContent'
import { ColorPalette, AppTheme } from './types'

// تعريف النوع للـ Electron API
declare global {
  interface Window {
    electronAPI: {
      saveFile: (data: string, defaultPath?: string) => Promise<string | null>
      loadFile: () => Promise<{ data: string; filePath: string } | null>
      openImage: () => Promise<string | null>
      onMenuOpenImage: (callback: () => void) => void
      onMenuSavePalette: (callback: () => void) => void
      removeAllListeners: (channel: string) => void
    }
  }
}

function App() {
  const [theme, setTheme] = useState<AppTheme>('light')
  const [currentPalette, setCurrentPalette] = useState<ColorPalette | null>(null)
  const [savedPalettes, setSavedPalettes] = useState<ColorPalette[]>([])
  const [activeTab, setActiveTab] = useState<'generator' | 'extractor' | 'favorites'>('generator')

  // تطبيق الثيم
  useEffect(() => {
    document.documentElement.setAttribute('data-theme', theme)
  }, [theme])

  // إعداد مستمعي أحداث Electron
  useEffect(() => {
    if (window.electronAPI) {
      // فتح صورة من القائمة
      window.electronAPI.onMenuOpenImage(() => {
        handleOpenImage()
      })

      // حفظ لوحة الألوان من القائمة
      window.electronAPI.onMenuSavePalette(() => {
        if (currentPalette) {
          handleSavePalette()
        }
      })

      // تنظيف المستمعين عند إلغاء التحميل
      return () => {
        window.electronAPI.removeAllListeners('menu-open-image')
        window.electronAPI.removeAllListeners('menu-save-palette')
      }
    }
  }, [currentPalette])

  // تبديل الثيم
  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light')
  }

  // فتح صورة
  const handleOpenImage = async () => {
    if (window.electronAPI) {
      try {
        const imagePath = await window.electronAPI.openImage()
        if (imagePath) {
          setActiveTab('extractor')
          // سيتم تمرير مسار الصورة لمكون استخراج الألوان
        }
      } catch (error) {
        console.error('خطأ في فتح الصورة:', error)
      }
    }
  }

  // حفظ لوحة الألوان
  const handleSavePalette = async () => {
    if (!currentPalette || !window.electronAPI) return

    try {
      const data = JSON.stringify(currentPalette, null, 2)
      const filePath = await window.electronAPI.saveFile(data, `${currentPalette.name}.json`)
      
      if (filePath) {
        console.log('تم حفظ لوحة الألوان في:', filePath)
      }
    } catch (error) {
      console.error('خطأ في حفظ لوحة الألوان:', error)
    }
  }

  // تحميل لوحة ألوان محفوظة
  const handleLoadPalette = async () => {
    if (!window.electronAPI) return

    try {
      const result = await window.electronAPI.loadFile()
      if (result) {
        const palette: ColorPalette = JSON.parse(result.data)
        setCurrentPalette(palette)
        console.log('تم تحميل لوحة الألوان من:', result.filePath)
      }
    } catch (error) {
      console.error('خطأ في تحميل لوحة الألوان:', error)
    }
  }

  // إضافة لوحة للمفضلة
  const handleAddToFavorites = (palette: ColorPalette) => {
    setSavedPalettes(prev => {
      const exists = prev.find(p => p.id === palette.id)
      if (exists) return prev
      return [...prev, palette]
    })
  }

  // حذف من المفضلة
  const handleRemoveFromFavorites = (paletteId: string) => {
    setSavedPalettes(prev => prev.filter(p => p.id !== paletteId))
  }

  return (
    <div className="app">
      <Sidebar
        activeTab={activeTab}
        onTabChange={setActiveTab}
        savedPalettes={savedPalettes}
        onLoadPalette={setCurrentPalette}
        onRemoveFromFavorites={handleRemoveFromFavorites}
      />
      
      <div className="main-content">
        <header className="main-header">
          <div className="flex items-center gap-4">
            <Palette className="w-6 h-6 text-primary-color" />
            <h1 className="text-xl font-semibold text-primary">
              مولد لوحات الألوان
            </h1>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleOpenImage}
              className="btn btn-secondary btn-icon"
              title="فتح صورة"
            >
              <Image className="w-4 h-4" />
            </button>
            
            {currentPalette && (
              <button
                onClick={handleSavePalette}
                className="btn btn-secondary btn-icon"
                title="حفظ لوحة الألوان"
              >
                <Save className="w-4 h-4" />
              </button>
            )}
            
            <button
              onClick={handleLoadPalette}
              className="btn btn-secondary"
              title="تحميل لوحة ألوان"
            >
              تحميل
            </button>
            
            <button
              onClick={toggleTheme}
              className="btn btn-secondary btn-icon"
              title={theme === 'light' ? 'الوضع الداكن' : 'الوضع الفاتح'}
            >
              {theme === 'light' ? <Moon className="w-4 h-4" /> : <Sun className="w-4 h-4" />}
            </button>
          </div>
        </header>
        
        <MainContent
          activeTab={activeTab}
          currentPalette={currentPalette}
          onPaletteChange={setCurrentPalette}
          onAddToFavorites={handleAddToFavorites}
        />
      </div>
    </div>
  )
}

export default App
