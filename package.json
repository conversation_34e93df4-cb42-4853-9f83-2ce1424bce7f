{"name": "color-palette-generator", "version": "1.0.0", "description": "برنامج سطح مكتب لتوليد وإدارة لوحات الألوان للمصممين", "main": "dist-electron/main.js", "scripts": {"dev": "vite", "build": "vite build && electron-builder", "build:dir": "vite build && electron-builder --dir", "preview": "vite preview", "electron": "wait-on tcp:5173 && cross-env IS_DEV=true electron .", "electron:pack": "electron-builder", "electron:dev": "concurrently \"npm run dev\" \"npm run electron\"", "electron:preview": "npm run build && electron .", "dist": "npm run build"}, "keywords": ["color", "palette", "design", "electron", "react"], "author": "Color Palette Generator", "license": "MIT", "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "typescript": "^5.2.2", "vite": "^5.0.8", "vite-plugin-electron": "^0.15.5", "vite-plugin-electron-renderer": "^0.14.5", "wait-on": "^7.2.0"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "lucide-react": "^0.294.0", "colorthief": "^2.4.0"}, "build": {"appId": "com.colorpalette.generator", "productName": "Color Palette Generator", "directories": {"output": "release"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*"], "mac": {"icon": "assets/icon.icns", "target": "dmg"}, "win": {"icon": "assets/icon.ico", "target": "nsis"}, "linux": {"icon": "assets/icon.png", "target": "AppImage"}}}