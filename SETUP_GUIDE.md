# دليل التثبيت والإعداد - Color Palette Generator

## 📋 الخطوات المطلوبة لتشغيل البرنامج

### الخطوة 1: تثبيت Node.js

1. **تحميل Node.js:**
   - اذهب إلى الموقع الرسمي: https://nodejs.org/
   - حمل النسخة **LTS** (Long Term Support) - الموصى بها
   - اختر النسخة المناسبة لنظام التشغيل الخاص بك (Windows x64)

2. **تثبيت Node.js:**
   - شغل ملف التثبيت الذي حملته
   - اتبع التعليمات (اضغط Next في كل خطوة)
   - تأكد من تحديد خيار "Add to PATH" أثناء التثبيت
   - انتظر حتى انتهاء التثبيت

3. **إعادة تشغيل الكمبيوتر:**
   - هذا مهم جداً لتطبيق التغييرات على PATH
   - أعد تشغيل الكمبيوتر بالكامل

### الخطوة 2: التحقق من التثبيت

1. **فتح Command Prompt:**
   - اضغط `Windows + R`
   - اكتب `cmd` واضغط Enter

2. **التحقق من Node.js:**
   ```bash
   node --version
   ```
   - يجب أن ترى رقم الإصدار (مثل: v18.17.0)

3. **التحقق من npm:**
   ```bash
   npm --version
   ```
   - يجب أن ترى رقم الإصدار (مثل: 9.6.7)

### الخطوة 3: تثبيت مكتبات البرنامج

1. **الانتقال لمجلد البرنامج:**
   ```bash
   cd "C:\Users\<USER>\Documents\augment-projects\pc app"
   ```

2. **تثبيت المكتبات:**
   ```bash
   npm install
   ```
   - انتظر حتى انتهاء التحميل (قد يستغرق بضع دقائق)

### الخطوة 4: تشغيل البرنامج

1. **تشغيل في وضع التطوير:**
   ```bash
   npm run electron:dev
   ```
   - سيفتح البرنامج في نافذة جديدة

2. **أو تشغيل الخادم فقط:**
   ```bash
   npm run dev
   ```
   - ثم في terminal آخر:
   ```bash
   npm run electron
   ```

### الخطوة 5: بناء ملف .exe للتوزيع

1. **بناء البرنامج:**
   ```bash
   npm run build
   ```

2. **إنشاء ملف التثبيت:**
   ```bash
   npm run dist
   ```
   - ستجد ملف .exe في مجلد `release/`

## 🔧 حل المشاكل الشائعة

### مشكلة: "npm is not recognized"
**السبب:** Node.js غير مثبت أو غير مضاف للـ PATH

**الحل:**
1. تأكد من تثبيت Node.js
2. أعد تشغيل الكمبيوتر
3. جرب فتح Command Prompt جديد

### مشكلة: "node is not recognized"
**السبب:** نفس المشكلة السابقة

**الحل:**
1. أعد تثبيت Node.js
2. تأكد من تحديد "Add to PATH" أثناء التثبيت
3. أعد تشغيل الكمبيوتر

### مشكلة: "npm install" يفشل
**السبب:** مشاكل في الشبكة أو الصلاحيات

**الحل:**
1. تأكد من الاتصال بالإنترنت
2. جرب تشغيل Command Prompt كـ Administrator
3. جرب:
   ```bash
   npm cache clean --force
   npm install
   ```

### مشكلة: البرنامج لا يفتح
**السبب:** لم يتم تثبيت المكتبات

**الحل:**
1. تأكد من تشغيل `npm install` بنجاح
2. تأكد من عدم وجود أخطاء في Terminal
3. جرب:
   ```bash
   npm run dev
   ```
   أولاً للتأكد من عمل الواجهة

## 📞 طلب المساعدة

إذا واجهت أي مشاكل:

1. **تأكد من اتباع جميع الخطوات بالترتيب**
2. **أعد تشغيل الكمبيوتر بعد تثبيت Node.js**
3. **تأكد من الاتصال بالإنترنت أثناء `npm install`**

## 🎯 نصائح إضافية

- **استخدم Command Prompt وليس PowerShell** إذا واجهت مشاكل
- **تأكد من وجود مساحة كافية على القرص الصلب** (على الأقل 1GB)
- **أغلق برامج الحماية مؤقتاً** إذا كانت تمنع التثبيت
- **استخدم اتصال إنترنت مستقر** أثناء `npm install`

---

**بعد اتباع هذه الخطوات، ستتمكن من تشغيل برنامج مولد لوحات الألوان بنجاح! 🎨**
