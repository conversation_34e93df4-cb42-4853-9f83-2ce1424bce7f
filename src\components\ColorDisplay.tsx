import React, { useState } from 'react'
import { Copy, Check } from 'lucide-react'
import { Color } from '../types'

interface ColorDisplayProps {
  colors: Color[]
  showCopyButtons?: boolean
  onColorClick?: (color: Color) => void
}

const ColorDisplay: React.FC<ColorDisplayProps> = ({
  colors,
  showCopyButtons = true,
  onColorClick
}) => {
  const [copiedColor, setCopiedColor] = useState<string | null>(null)

  const copyToClipboard = async (text: string, colorId: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopiedColor(colorId)
      setTimeout(() => setCopiedColor(null), 2000)
    } catch (error) {
      console.error('فشل في نسخ النص:', error)
    }
  }

  const formatRgb = (rgb: { r: number; g: number; b: number }) => {
    return `rgb(${Math.round(rgb.r)}, ${Math.round(rgb.g)}, ${Math.round(rgb.b)})`
  }

  const formatHsl = (hsl: { h: number; s: number; l: number }) => {
    return `hsl(${Math.round(hsl.h)}, ${Math.round(hsl.s)}%, ${Math.round(hsl.l)}%)`
  }

  return (
    <div className="color-grid">
      {colors.map((color) => (
        <div
          key={color.id}
          className="color-item"
          onClick={() => onColorClick?.(color)}
        >
          {/* معاينة اللون */}
          <div
            className="color-preview"
            style={{ backgroundColor: color.hex }}
          />
          
          {/* معلومات اللون */}
          <div className="color-info">
            {/* اسم اللون */}
            {color.name && (
              <div className="text-xs font-medium text-primary mb-2">
                {color.name}
              </div>
            )}
            
            {/* أكواد الألوان */}
            <div className="space-y-2">
              {/* HEX */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="color-code">{color.hex.toUpperCase()}</div>
                  <div className="color-name">HEX</div>
                </div>
                {showCopyButtons && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      copyToClipboard(color.hex, `${color.id}-hex`)
                    }}
                    className="p-1 text-muted hover:text-primary transition-colors"
                    title="نسخ HEX"
                  >
                    {copiedColor === `${color.id}-hex` ? (
                      <Check className="w-3 h-3 text-success-color" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                  </button>
                )}
              </div>
              
              {/* RGB */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="color-code text-xs">
                    {Math.round(color.rgb.r)}, {Math.round(color.rgb.g)}, {Math.round(color.rgb.b)}
                  </div>
                  <div className="color-name">RGB</div>
                </div>
                {showCopyButtons && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      copyToClipboard(formatRgb(color.rgb), `${color.id}-rgb`)
                    }}
                    className="p-1 text-muted hover:text-primary transition-colors"
                    title="نسخ RGB"
                  >
                    {copiedColor === `${color.id}-rgb` ? (
                      <Check className="w-3 h-3 text-success-color" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                  </button>
                )}
              </div>
              
              {/* HSL */}
              <div className="flex items-center justify-between">
                <div>
                  <div className="color-code text-xs">
                    {Math.round(color.hsl.h)}°, {Math.round(color.hsl.s)}%, {Math.round(color.hsl.l)}%
                  </div>
                  <div className="color-name">HSL</div>
                </div>
                {showCopyButtons && (
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      copyToClipboard(formatHsl(color.hsl), `${color.id}-hsl`)
                    }}
                    className="p-1 text-muted hover:text-primary transition-colors"
                    title="نسخ HSL"
                  >
                    {copiedColor === `${color.id}-hsl` ? (
                      <Check className="w-3 h-3 text-success-color" />
                    ) : (
                      <Copy className="w-3 h-3" />
                    )}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export default ColorDisplay
