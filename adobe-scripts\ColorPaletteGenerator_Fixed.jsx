/*
 * Color Palette Generator for Adobe Photoshop & Illustrator - FIXED VERSION
 * مولد لوحات الألوان لبرامج Adobe - النسخة المحسنة
 * 
 * إصدار محسن يتجنب مشاكل التوافق مع JSX
 */

// التحقق من البرنامج المستخدم
var isPhotoshop = (app.name.indexOf("Photoshop") >= 0);
var isIllustrator = (app.name.indexOf("Illustrator") >= 0);

if (!isPhotoshop && !isIllustrator) {
    alert("هذا السكريبت يعمل فقط مع Adobe Photoshop أو Illustrator");
    exit();
}

// إعدادات عامة
var SCRIPT_NAME = "مولد لوحات الألوان";
var SCRIPT_VERSION = "1.0.1 - Fixed";

// متغيرات عامة
var dialog;
var currentPalette = [];

// أنواع التناغم اللوني (مصفوفة بدلاً من كائن)
var harmonyTypes = [
    {key: "complementary", name: "مكمل"},
    {key: "analogous", name: "متجاور"},
    {key: "triadic", name: "ثلاثي"},
    {key: "tetradic", name: "رباعي"},
    {key: "monochromatic", name: "أحادي اللون"},
    {key: "splitComplementary", name: "مكمل منقسم"}
];

// الوظائف المساعدة للألوان
function hexToRgb(hex) {
    // إزالة # إذا كانت موجودة
    hex = hex.replace("#", "");
    
    if (hex.length === 3) {
        hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }
    
    if (hex.length !== 6) {
        return null;
    }
    
    var r = parseInt(hex.substring(0, 2), 16);
    var g = parseInt(hex.substring(2, 4), 16);
    var b = parseInt(hex.substring(4, 6), 16);
    
    if (isNaN(r) || isNaN(g) || isNaN(b)) {
        return null;
    }
    
    return {r: r, g: g, b: b};
}

function rgbToHex(r, g, b) {
    function componentToHex(c) {
        var hex = Math.round(c).toString(16);
        return hex.length == 1 ? "0" + hex : hex;
    }
    return "#" + componentToHex(r) + componentToHex(g) + componentToHex(b);
}

function rgbToHsl(r, g, b) {
    r /= 255;
    g /= 255;
    b /= 255;
    
    var max = Math.max(r, g, b);
    var min = Math.min(r, g, b);
    var h, s, l = (max + min) / 2;
    
    if (max == min) {
        h = s = 0;
    } else {
        var d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
    }
    
    return {
        h: h * 360,
        s: s * 100,
        l: l * 100
    };
}

function hslToRgb(h, s, l) {
    h /= 360;
    s /= 100;
    l /= 100;
    
    var r, g, b;
    
    if (s == 0) {
        r = g = b = l;
    } else {
        function hue2rgb(p, q, t) {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        }
        
        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        var p = 2 * l - q;
        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
    }
    
    return {
        r: Math.round(r * 255),
        g: Math.round(g * 255),
        b: Math.round(b * 255)
    };
}

// التحقق من صحة كود اللون
function isValidHexColor(hex) {
    if (!hex || typeof hex !== "string") return false;
    
    // إزالة # إذا كانت موجودة
    hex = hex.replace("#", "");
    
    // التحقق من الطول والأحرف الصحيحة
    return /^[0-9A-Fa-f]{3}$|^[0-9A-Fa-f]{6}$/.test(hex);
}

// توليد لوحة ألوان مبسطة
function generateColorPalette(baseColor, harmonyType, colorCount) {
    var colors = [];
    var baseRgb = hexToRgb(baseColor);
    
    if (!baseRgb) {
        throw new Error("كود اللون غير صحيح");
    }
    
    var baseHsl = rgbToHsl(baseRgb.r, baseRgb.g, baseRgb.b);
    
    // إضافة اللون الأساسي
    colors.push({
        hex: baseColor.toUpperCase(),
        rgb: baseRgb,
        name: "اللون الأساسي"
    });
    
    // توليد الألوان حسب النوع
    switch (harmonyType) {
        case "complementary":
            // لون مكمل
            var compHue = (baseHsl.h + 180) % 360;
            var compRgb = hslToRgb(compHue, baseHsl.s, baseHsl.l);
            colors.push({
                hex: rgbToHex(compRgb.r, compRgb.g, compRgb.b),
                rgb: compRgb,
                name: "مكمل"
            });
            
            // إضافة تدرجات
            for (var i = 2; i < colorCount; i++) {
                var lightness = Math.max(10, Math.min(90, baseHsl.l + (i - colorCount/2) * 20));
                var rgb = hslToRgb(baseHsl.h, baseHsl.s * 0.8, lightness);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "تدرج " + i
                });
            }
            break;
            
        case "analogous":
            // ألوان متجاورة
            for (var i = 1; i < colorCount; i++) {
                var hue = (baseHsl.h + (i * 25)) % 360;
                var rgb = hslToRgb(hue, baseHsl.s, baseHsl.l);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "متجاور " + i
                });
            }
            break;
            
        case "triadic":
            // ثلاثة ألوان متباعدة
            for (var i = 1; i < Math.min(3, colorCount); i++) {
                var hue = (baseHsl.h + (i * 120)) % 360;
                var rgb = hslToRgb(hue, baseHsl.s, baseHsl.l);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "ثلاثي " + i
                });
            }
            
            // إضافة تدرجات إضافية
            for (var i = 3; i < colorCount; i++) {
                var lightness = Math.max(15, Math.min(85, baseHsl.l + (Math.random() - 0.5) * 40));
                var rgb = hslToRgb(baseHsl.h, baseHsl.s * 0.9, lightness);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "تدرج " + i
                });
            }
            break;
            
        case "monochromatic":
            // تدرجات من نفس اللون
            for (var i = 1; i < colorCount; i++) {
                var lightness = 20 + (i * 60 / (colorCount - 1));
                var rgb = hslToRgb(baseHsl.h, baseHsl.s, lightness);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "تدرج " + i
                });
            }
            break;
            
        default:
            // افتراضي - مكمل
            var compHue = (baseHsl.h + 180) % 360;
            var compRgb = hslToRgb(compHue, baseHsl.s, baseHsl.l);
            colors.push({
                hex: rgbToHex(compRgb.r, compRgb.g, compRgb.b),
                rgb: compRgb,
                name: "مكمل"
            });
    }
    
    return colors.slice(0, colorCount);
}

// رسم مربعات الألوان
function drawColorShapes(palette) {
    try {
        if (isPhotoshop) {
            drawColorShapesPhotoshop(palette);
        } else if (isIllustrator) {
            drawColorShapesIllustrator(palette);
        }
    } catch (error) {
        alert("خطأ في رسم الألوان: " + error.message);
    }
}

// رسم مربعات الألوان في Photoshop
function drawColorShapesPhotoshop(palette) {
    var doc = app.activeDocument;
    var startX = 50;
    var startY = 50;
    var squareSize = 100;
    var spacing = 20;

    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var x = startX + (i * (squareSize + spacing));
        var y = startY;

        // إنشاء مستطيل
        var bounds = [
            [x, y],
            [x + squareSize, y],
            [x + squareSize, y + squareSize],
            [x, y + squareSize]
        ];

        // إنشاء طبقة جديدة
        var layer = doc.artLayers.add();
        layer.name = color.name + " - " + color.hex;

        // تحديد المنطقة
        doc.selection.select(bounds);

        // تعيين اللون
        var fillColor = new SolidColor();
        fillColor.rgb.red = color.rgb.r;
        fillColor.rgb.green = color.rgb.g;
        fillColor.rgb.blue = color.rgb.b;

        // تعبئة بالألوان
        doc.selection.fill(fillColor);
        doc.selection.deselect();

        // إضافة نص مع كود اللون
        var textLayer = doc.artLayers.add();
        textLayer.kind = LayerKind.TEXT;
        textLayer.name = "نص " + color.name;

        var textItem = textLayer.textItem;
        textItem.contents = color.hex;
        textItem.position = [x, y + squareSize + 20];
        textItem.size = 12;

        var textColor = new SolidColor();
        textColor.rgb.red = 0;
        textColor.rgb.green = 0;
        textColor.rgb.blue = 0;
        textItem.color = textColor;
    }
}

// رسم مربعات الألوان في Illustrator
function drawColorShapesIllustrator(palette) {
    var doc = app.activeDocument;
    var startX = 50;
    var startY = -50;
    var squareSize = 100;
    var spacing = 20;

    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var x = startX + (i * (squareSize + spacing));
        var y = startY;

        // إنشاء مستطيل
        var rect = doc.pathItems.rectangle();
        rect.top = y;
        rect.left = x;
        rect.width = squareSize;
        rect.height = squareSize;

        // تعيين اللون
        var fillColor = new RGBColor();
        fillColor.red = color.rgb.r;
        fillColor.green = color.rgb.g;
        fillColor.blue = color.rgb.b;

        rect.fillColor = fillColor;
        rect.filled = true;
        rect.stroked = true;

        var strokeColor = new RGBColor();
        strokeColor.red = 200;
        strokeColor.green = 200;
        strokeColor.blue = 200;
        rect.strokeColor = strokeColor;
        rect.strokeWidth = 1;

        // إضافة نص مع كود اللون
        var textFrame = doc.textFrames.add();
        textFrame.contents = color.hex + "\n" + color.name;
        textFrame.top = y - squareSize - 10;
        textFrame.left = x;

        var textColor = new RGBColor();
        textColor.red = 0;
        textColor.green = 0;
        textColor.blue = 0;
        textFrame.textRange.characterAttributes.fillColor = textColor;
        textFrame.textRange.characterAttributes.size = 10;
    }
}

// إضافة الألوان إلى Swatches
function addColorsToSwatches(palette) {
    try {
        if (isPhotoshop) {
            addColorsToSwatchesPhotoshop(palette);
        } else if (isIllustrator) {
            addColorsToSwatchesIllustrator(palette);
        }
    } catch (error) {
        alert("خطأ في إضافة الألوان للـ Swatches: " + error.message);
    }
}

// إضافة الألوان إلى Swatches في Photoshop
function addColorsToSwatchesPhotoshop(palette) {
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];

        var solidColor = new SolidColor();
        solidColor.rgb.red = color.rgb.r;
        solidColor.rgb.green = color.rgb.g;
        solidColor.rgb.blue = color.rgb.b;

        // إضافة اللون للـ Foreground
        app.foregroundColor = solidColor;
    }

    alert("تم تعيين الألوان. يمكنك إضافتها للـ Swatches يدوياً من Color panel");
}

// إضافة الألوان إلى Swatches في Illustrator
function addColorsToSwatchesIllustrator(palette) {
    var doc = app.activeDocument;

    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];

        // إنشاء لون جديد
        var newColor = doc.colors.add();
        newColor.name = color.name + " - " + color.hex;

        var rgbColor = new RGBColor();
        rgbColor.red = color.rgb.r;
        rgbColor.green = color.rgb.g;
        rgbColor.blue = color.rgb.b;

        newColor.color = rgbColor;

        // إنشاء Swatch
        var newSwatch = doc.swatches.add();
        newSwatch.name = color.name;
        newSwatch.color = newColor;
    }

    alert("تم إضافة " + palette.length + " لون إلى Swatches بنجاح!");
}

// إنشاء واجهة المستخدم المبسطة
function createUI() {
    dialog = new Window("dialog", SCRIPT_NAME + " v" + SCRIPT_VERSION);
    dialog.orientation = "column";
    dialog.alignChildren = "fill";
    dialog.spacing = 10;
    dialog.margins = 16;

    // مجموعة إعدادات التوليد
    var generateGroup = dialog.add("panel", undefined, "إعدادات توليد لوحة الألوان");
    generateGroup.orientation = "column";
    generateGroup.alignChildren = "fill";
    generateGroup.spacing = 10;
    generateGroup.margins = 10;

    // اللون الأساسي
    var colorGroup = generateGroup.add("group");
    colorGroup.add("statictext", undefined, "اللون الأساسي:");
    var colorInput = colorGroup.add("edittext", undefined, "#6366f1");
    colorInput.characters = 10;
    var randomBtn = colorGroup.add("button", undefined, "عشوائي");

    // نوع التناغم
    var harmonyGroup = generateGroup.add("group");
    harmonyGroup.add("statictext", undefined, "نوع التناغم:");
    var harmonyDropdown = harmonyGroup.add("dropdownlist");
    for (var i = 0; i < harmonyTypes.length; i++) {
        harmonyDropdown.add("item", harmonyTypes[i].name);
    }
    harmonyDropdown.selection = 0;

    // عدد الألوان
    var countGroup = generateGroup.add("group");
    countGroup.add("statictext", undefined, "عدد الألوان:");
    var colorCountSlider = countGroup.add("slider", undefined, 5, 3, 8);
    var colorCountText = countGroup.add("statictext", undefined, "5");
    colorCountSlider.onChanging = function() {
        colorCountText.text = Math.round(colorCountSlider.value);
    };

    // مجموعة الإجراءات
    var actionGroup = dialog.add("panel", undefined, "الإجراءات");
    actionGroup.orientation = "column";
    actionGroup.alignChildren = "fill";
    actionGroup.spacing = 10;
    actionGroup.margins = 10;

    var drawShapesCheck = actionGroup.add("checkbox", undefined, "رسم مربعات الألوان");
    var addSwatchesCheck = actionGroup.add("checkbox", undefined, "إضافة إلى Swatches");

    drawShapesCheck.value = true;
    addSwatchesCheck.value = true;

    // أزرار التحكم
    var buttonGroup = dialog.add("group");
    buttonGroup.alignment = "center";
    var generateBtn = buttonGroup.add("button", undefined, "توليد لوحة الألوان");
    var cancelBtn = buttonGroup.add("button", undefined, "إلغاء");

    // أحداث الأزرار
    randomBtn.onClick = function() {
        colorInput.text = generateRandomColor();
    };

    generateBtn.onClick = function() {
        try {
            var baseColor = colorInput.text;
            var harmonyIndex = harmonyDropdown.selection.index;
            var harmonyKey = harmonyTypes[harmonyIndex].key;
            var colorCount = Math.round(colorCountSlider.value);

            // التحقق من صحة كود اللون
            if (!isValidHexColor(baseColor)) {
                alert("كود اللون غير صحيح. يرجى استخدام تنسيق HEX صحيح مثل: #FF5733");
                return;
            }

            // توليد لوحة الألوان
            currentPalette = generateColorPalette(baseColor, harmonyKey, colorCount);

            if (drawShapesCheck.value) {
                drawColorShapes(currentPalette);
            }

            if (addSwatchesCheck.value) {
                addColorsToSwatches(currentPalette);
            }

            // عرض النتائج
            var message = "تم توليد لوحة الألوان بنجاح!\n\n";
            message += "عدد الألوان: " + currentPalette.length + "\n";
            message += "نوع التناغم: " + harmonyTypes[harmonyIndex].name + "\n\n";
            message += "الألوان المولدة:\n";

            for (var i = 0; i < Math.min(5, currentPalette.length); i++) {
                var color = currentPalette[i];
                message += "• " + color.name + ": " + color.hex + "\n";
            }

            if (currentPalette.length > 5) {
                message += "... و " + (currentPalette.length - 5) + " ألوان أخرى";
            }

            alert(message);
            dialog.close();

        } catch (error) {
            alert("حدث خطأ: " + error.message + "\n\nتأكد من:\n• صحة كود اللون\n• وجود مستند مفتوح\n• توافق إصدار البرنامج");
        }
    };

    cancelBtn.onClick = function() {
        dialog.close();
    };

    return dialog;
}

// توليد لون عشوائي
function generateRandomColor() {
    var colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#F39C12", "#E74C3C", "#9B59B6", "#3498DB"];
    return colors[Math.floor(Math.random() * colors.length)];
}

// الوظيفة الرئيسية
function main() {
    try {
        // التحقق من وجود مستند مفتوح
        if (app.documents.length == 0) {
            var createDoc = confirm("لا يوجد مستند مفتوح. هل تريد إنشاء مستند جديد؟");
            if (createDoc) {
                if (isPhotoshop) {
                    app.documents.add(800, 600, 72, "Color Palette Workspace", NewDocumentMode.RGB);
                } else if (isIllustrator) {
                    var doc = app.documents.add(DocumentColorSpace.RGB, 800, 600);
                    doc.name = "Color Palette Workspace";
                }
            } else {
                return;
            }
        }

        // عرض رسالة ترحيب
        var welcomeMessage = "مرحباً بك في مولد لوحات الألوان!\n\n";
        welcomeMessage += "البرنامج: " + app.name + "\n";
        welcomeMessage += "هذا السكريبت يساعدك في توليد لوحات ألوان متناسقة\n\n";
        welcomeMessage += "المميزات:\n";
        welcomeMessage += "• 6 أنواع من نظريات الألوان\n";
        welcomeMessage += "• رسم مربعات الألوان في المشروع\n";
        welcomeMessage += "• إضافة الألوان إلى Swatches\n\n";
        welcomeMessage += "هل تريد المتابعة؟";

        if (!confirm(welcomeMessage)) {
            return;
        }

        // إنشاء وعرض واجهة المستخدم
        var ui = createUI();
        ui.show();

    } catch (error) {
        alert("خطأ في تشغيل السكريبت: " + error.message + "\n\nيرجى التأكد من:\n• وجود مستند مفتوح\n• توافق إصدار البرنامج\n• صلاحيات تشغيل السكريبت");
    }
}

// تشغيل السكريبت
main();
