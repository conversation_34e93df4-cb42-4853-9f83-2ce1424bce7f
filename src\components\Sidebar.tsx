import React from 'react'
import { Pa<PERSON>, <PERSON>, Heart, Trash2 } from 'lucide-react'
import { ColorPalette, AppTab } from '../types'

interface SidebarProps {
  activeTab: AppTab
  onTabChange: (tab: AppTab) => void
  savedPalettes: ColorPalette[]
  onLoadPalette: (palette: ColorPalette) => void
  onRemoveFromFavorites: (paletteId: string) => void
}

const Sidebar: React.FC<SidebarProps> = ({
  activeTab,
  onTabChange,
  savedPalettes,
  onLoadPalette,
  onRemoveFromFavorites
}) => {
  const tabs = [
    {
      id: 'generator' as AppTab,
      label: 'مولد الألوان',
      icon: Palette,
      description: 'توليد لوحات ألوان تلقائيًا'
    },
    {
      id: 'extractor' as AppTab,
      label: 'استخراج الألوان',
      icon: Image,
      description: 'استخراج الألوان من الصور'
    },
    {
      id: 'favorites' as AppTab,
      label: 'المفضلة',
      icon: Heart,
      description: 'لوحات الألوان المحفوظة'
    }
  ]

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date))
  }

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h2 className="sidebar-title">مولد لوحات الألوان</h2>
        <p className="sidebar-subtitle">أداة احترافية للمصممين</p>
      </div>

      <div className="sidebar-content">
        {/* التبويبات */}
        <div className="mb-6">
          <h3 className="text-sm font-medium text-secondary mb-3">الأدوات</h3>
          <div className="space-y-1">
            {tabs.map((tab) => {
              const Icon = tab.icon
              const isActive = activeTab === tab.id
              
              return (
                <button
                  key={tab.id}
                  onClick={() => onTabChange(tab.id)}
                  className={`w-full flex items-center gap-3 p-3 rounded-lg text-right transition-all ${
                    isActive
                      ? 'bg-primary-color text-white shadow-md'
                      : 'text-secondary hover:bg-tertiary'
                  }`}
                >
                  <Icon className="w-5 h-5 flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="font-medium">{tab.label}</div>
                    <div className={`text-xs ${isActive ? 'text-white/80' : 'text-muted'}`}>
                      {tab.description}
                    </div>
                  </div>
                </button>
              )
            })}
          </div>
        </div>

        {/* المفضلة */}
        {activeTab === 'favorites' && (
          <div>
            <h3 className="text-sm font-medium text-secondary mb-3">
              لوحات الألوان المحفوظة ({savedPalettes.length})
            </h3>
            
            {savedPalettes.length === 0 ? (
              <div className="text-center py-8">
                <Heart className="w-12 h-12 text-muted mx-auto mb-3" />
                <p className="text-muted text-sm">لا توجد لوحات محفوظة</p>
                <p className="text-muted text-xs mt-1">
                  احفظ لوحات الألوان المفضلة لديك
                </p>
              </div>
            ) : (
              <div className="space-y-2">
                {savedPalettes.map((palette) => (
                  <div
                    key={palette.id}
                    className="bg-primary border border-border rounded-lg p-3 hover:shadow-md transition-all cursor-pointer group"
                    onClick={() => onLoadPalette(palette)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-primary text-sm truncate flex-1">
                        {palette.name}
                      </h4>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          onRemoveFromFavorites(palette.id)
                        }}
                        className="opacity-0 group-hover:opacity-100 text-muted hover:text-error transition-all p-1"
                        title="حذف من المفضلة"
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    </div>
                    
                    {/* معاينة الألوان */}
                    <div className="flex gap-1 mb-2">
                      {palette.colors.slice(0, 5).map((color, index) => (
                        <div
                          key={index}
                          className="w-4 h-4 rounded border border-border flex-shrink-0"
                          style={{ backgroundColor: color.hex }}
                          title={color.hex}
                        />
                      ))}
                      {palette.colors.length > 5 && (
                        <div className="w-4 h-4 rounded border border-border flex-shrink-0 bg-tertiary flex items-center justify-center">
                          <span className="text-xs text-muted">+{palette.colors.length - 5}</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center justify-between text-xs text-muted">
                      <span>{palette.colors.length} لون</span>
                      <span>{formatDate(palette.createdAt)}</span>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* معلومات إضافية */}
        {activeTab !== 'favorites' && (
          <div className="mt-auto pt-6 border-t border-border">
            <div className="text-xs text-muted space-y-1">
              <p>💡 نصيحة: استخدم Ctrl+O لفتح صورة</p>
              <p>💾 احفظ لوحاتك المفضلة بـ Ctrl+S</p>
              <p>🎨 جرب نظريات الألوان المختلفة</p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Sidebar
