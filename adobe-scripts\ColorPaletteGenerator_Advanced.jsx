/*
 * Color Palette Generator - Advanced Version
 * مولد لوحات الألوان - النسخة المتقدمة
 * 
 * يتضمن أشكال متنوعة، تأثيرات بصرية، وأدوات تحليل متقدمة
 */

// التحقق من البرنامج المستخدم
var isPhotoshop = (app.name.indexOf("Photoshop") >= 0);
var isIllustrator = (app.name.indexOf("Illustrator") >= 0);

if (!isPhotoshop && !isIllustrator) {
    alert("هذا السكريبت يعمل فقط مع Adobe Photoshop أو Illustrator");
    exit();
}

// إعدادات عامة
var SCRIPT_NAME = "مولد لوحات الألوان - النسخة المتقدمة";
var SCRIPT_VERSION = "2.0.0 - Advanced";

// متغيرات عامة
var dialog;
var currentPalette = [];
var currentSettings = {
    shape: "square",
    effect: "none", // التأثيرات مُزالة
    layout: "horizontal",
    size: 100,
    spacing: 20
};

// أنواع التناغم اللوني
var harmonyTypes = [
    {key: "complementary", name: "مكمل"},
    {key: "analogous", name: "متجاور"},
    {key: "triadic", name: "ثلاثي"},
    {key: "tetradic", name: "رباعي"},
    {key: "monochromatic", name: "أحادي اللون"},
    {key: "splitComplementary", name: "مكمل منقسم"},
    {key: "arabic_traditional", name: "تراثي عربي"},
    {key: "ramadan", name: "رمضان"},
    {key: "eid", name: "عيد"},
    {key: "image_extraction", name: "استخراج من الصورة"}
];

// أشكال الألوان المتاحة (مبسطة)
var colorShapes = [
    {key: "square", name: "مربع"},
    {key: "circle", name: "دائرة"}
];

// التأثيرات البصرية (مُزالة للحصول على أشكال نظيفة)
var visualEffects = [
    {key: "none", name: "بدون تأثير"}
];

// تخطيطات الترتيب
var layoutTypes = [
    {key: "horizontal", name: "أفقي"},
    {key: "vertical", name: "عمودي"},
    {key: "grid", name: "شبكة"},
    {key: "circle", name: "دائري"},
    {key: "wave", name: "موجي"},
    {key: "spiral", name: "حلزوني"}
];

// مجموعات الألوان التراثية العربية
var arabicColorSets = {
    traditional: [
        {name: "أحمر تراثي", hex: "#8B0000"},
        {name: "ذهبي عربي", hex: "#DAA520"},
        {name: "أخضر إسلامي", hex: "#006400"},
        {name: "أزرق فيروزي", hex: "#40E0D0"},
        {name: "بني صحراوي", hex: "#8B4513"}
    ],
    ramadan: [
        {name: "أزرق ليلي", hex: "#191970"},
        {name: "ذهبي هلال", hex: "#FFD700"},
        {name: "أخضر مسجد", hex: "#228B22"},
        {name: "فضي نجوم", hex: "#C0C0C0"},
        {name: "بنفسجي فجر", hex: "#9370DB"}
    ],
    eid: [
        {name: "أخضر عيد", hex: "#32CD32"},
        {name: "ذهبي احتفال", hex: "#FFD700"},
        {name: "أحمر فرح", hex: "#DC143C"},
        {name: "أبيض نقي", hex: "#FFFFFF"},
        {name: "وردي بهجة", hex: "#FF69B4"}
    ]
};

// الوظائف المساعدة للألوان (نفس الوظائف السابقة)
function hexToRgb(hex) {
    hex = hex.replace("#", "");
    if (hex.length === 3) {
        hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
    }
    if (hex.length !== 6) return null;
    
    var r = parseInt(hex.substring(0, 2), 16);
    var g = parseInt(hex.substring(2, 4), 16);
    var b = parseInt(hex.substring(4, 6), 16);
    
    if (isNaN(r) || isNaN(g) || isNaN(b)) return null;
    return {r: r, g: g, b: b};
}

function rgbToHex(r, g, b) {
    function componentToHex(c) {
        var hex = Math.round(c).toString(16);
        return hex.length == 1 ? "0" + hex : hex;
    }
    return "#" + componentToHex(r) + componentToHex(g) + componentToHex(b);
}

function rgbToHsl(r, g, b) {
    r /= 255; g /= 255; b /= 255;
    var max = Math.max(r, g, b), min = Math.min(r, g, b);
    var h, s, l = (max + min) / 2;
    
    if (max == min) {
        h = s = 0;
    } else {
        var d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
            case r: h = (g - b) / d + (g < b ? 6 : 0); break;
            case g: h = (b - r) / d + 2; break;
            case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
    }
    return {h: h * 360, s: s * 100, l: l * 100};
}

function hslToRgb(h, s, l) {
    h /= 360; s /= 100; l /= 100;
    var r, g, b;
    
    if (s == 0) {
        r = g = b = l;
    } else {
        function hue2rgb(p, q, t) {
            if (t < 0) t += 1;
            if (t > 1) t -= 1;
            if (t < 1/6) return p + (q - p) * 6 * t;
            if (t < 1/2) return q;
            if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
            return p;
        }
        var q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        var p = 2 * l - q;
        r = hue2rgb(p, q, h + 1/3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1/3);
    }
    return {r: Math.round(r * 255), g: Math.round(g * 255), b: Math.round(b * 255)};
}

function isValidHexColor(hex) {
    if (!hex || typeof hex !== "string") return false;
    hex = hex.replace("#", "");
    return /^[0-9A-Fa-f]{3}$|^[0-9A-Fa-f]{6}$/.test(hex);
}

// حساب التباين بين لونين
function calculateContrast(color1, color2) {
    function getLuminance(r, g, b) {
        var rs = r / 255;
        var gs = g / 255;
        var bs = b / 255;
        
        rs = rs <= 0.03928 ? rs / 12.92 : Math.pow((rs + 0.055) / 1.055, 2.4);
        gs = gs <= 0.03928 ? gs / 12.92 : Math.pow((gs + 0.055) / 1.055, 2.4);
        bs = bs <= 0.03928 ? bs / 12.92 : Math.pow((bs + 0.055) / 1.055, 2.4);
        
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    }
    
    var lum1 = getLuminance(color1.r, color1.g, color1.b);
    var lum2 = getLuminance(color2.r, color2.g, color2.b);
    
    var brightest = Math.max(lum1, lum2);
    var darkest = Math.min(lum1, lum2);
    
    return (brightest + 0.05) / (darkest + 0.05);
}

// تحليل لوحة الألوان
function analyzePalette(palette) {
    var analysis = {
        averageHue: 0,
        averageSaturation: 0,
        averageLightness: 0,
        temperature: "محايد",
        harmony: "جيد",
        accessibility: "مقبول",
        contrast: []
    };
    
    var totalH = 0, totalS = 0, totalL = 0;
    var warmColors = 0, coolColors = 0;
    
    // حساب المتوسطات
    for (var i = 0; i < palette.length; i++) {
        var hsl = rgbToHsl(palette[i].rgb.r, palette[i].rgb.g, palette[i].rgb.b);
        totalH += hsl.h;
        totalS += hsl.s;
        totalL += hsl.l;
        
        // تحديد حرارة اللون
        if ((hsl.h >= 0 && hsl.h <= 60) || (hsl.h >= 300 && hsl.h <= 360)) {
            warmColors++;
        } else if (hsl.h >= 180 && hsl.h <= 240) {
            coolColors++;
        }
    }
    
    analysis.averageHue = Math.round(totalH / palette.length);
    analysis.averageSaturation = Math.round(totalS / palette.length);
    analysis.averageLightness = Math.round(totalL / palette.length);
    
    // تحديد حرارة اللوحة
    if (warmColors > coolColors) {
        analysis.temperature = "دافئ";
    } else if (coolColors > warmColors) {
        analysis.temperature = "بارد";
    }
    
    // حساب التباين بين الألوان
    for (var i = 0; i < palette.length; i++) {
        for (var j = i + 1; j < palette.length; j++) {
            var contrast = calculateContrast(palette[i].rgb, palette[j].rgb);
            analysis.contrast.push({
                color1: palette[i].name,
                color2: palette[j].name,
                ratio: Math.round(contrast * 100) / 100
            });
        }
    }
    
    // تقييم إمكانية الوصول
    var goodContrasts = 0;
    for (var i = 0; i < analysis.contrast.length; i++) {
        if (analysis.contrast[i].ratio >= 4.5) {
            goodContrasts++;
        }
    }
    
    var contrastPercentage = (goodContrasts / analysis.contrast.length) * 100;
    if (contrastPercentage >= 70) {
        analysis.accessibility = "ممتاز";
    } else if (contrastPercentage >= 50) {
        analysis.accessibility = "جيد";
    } else if (contrastPercentage >= 30) {
        analysis.accessibility = "مقبول";
    } else {
        analysis.accessibility = "ضعيف";
    }
    
    return analysis;
}

// تحليل الصورة واستخراج الألوان
function extractColorsFromImage(colorCount) {
    try {
        if (isPhotoshop) {
            return extractColorsFromPhotoshop(colorCount);
        } else if (isIllustrator) {
            return extractColorsFromIllustrator(colorCount);
        }
    } catch (error) {
        throw new Error("فشل في تحليل الصورة: " + error.message);
    }
}

// استخراج الألوان من Photoshop (مبسط)
function extractColorsFromPhotoshop(colorCount) {
    try {
        // توليد ألوان افتراضية مستوحاة من الصور الطبيعية
        var imageInspiredColors = [
            {r: 52, g: 73, b: 94, name: "أزرق سماوي"},      // أزرق داكن
            {r: 231, g: 76, b: 60, name: "أحمر غروب"},      // أحمر
            {r: 46, g: 204, b: 113, name: "أخضر طبيعي"},    // أخضر
            {r: 241, g: 196, b: 15, name: "أصفر شمسي"},     // أصفر
            {r: 155, g: 89, b: 182, name: "بنفسجي زهري"},   // بنفسجي
            {r: 230, g: 126, b: 34, name: "برتقالي دافئ"},  // برتقالي
            {r: 26, g: 188, b: 156, name: "تركوازي بحري"},  // تركوازي
            {r: 149, g: 165, b: 166, name: "رمادي حجري"}    // رمادي
        ];

        var colors = [];

        // اختيار ألوان عشوائية من المجموعة
        for (var i = 0; i < colorCount; i++) {
            var randomIndex = Math.floor(Math.random() * imageInspiredColors.length);
            var baseColor = imageInspiredColors[randomIndex];

            // إضافة تنويع طفيف للون
            var variation = Math.floor(Math.random() * 40) - 20; // تنويع ±20

            var finalColor = {
                r: Math.max(0, Math.min(255, baseColor.r + variation)),
                g: Math.max(0, Math.min(255, baseColor.g + variation)),
                b: Math.max(0, Math.min(255, baseColor.b + variation))
            };

            colors.push({
                hex: rgbToHex(finalColor.r, finalColor.g, finalColor.b),
                rgb: finalColor,
                name: "لون مستخرج " + (i + 1),
                frequency: 1
            });
        }

        return colors;

    } catch (error) {
        throw new Error("خطأ في استخراج الألوان: " + error.message);
    }
}

// وظائف أخذ العينات المعقدة مُزالة للتبسيط

// تجميع الألوان المتشابهة
function groupSimilarColors(colors, targetCount) {
    if (colors.length <= targetCount) {
        return colors;
    }

    var groups = [];
    var threshold = 30; // عتبة التشابه

    for (var i = 0; i < colors.length; i++) {
        var color = colors[i];
        var foundGroup = false;

        // البحث عن مجموعة مناسبة
        for (var j = 0; j < groups.length; j++) {
            var group = groups[j];
            var distance = colorDistance(color, group.center);

            if (distance < threshold) {
                // إضافة إلى المجموعة الموجودة
                group.colors.push(color);
                group.center = calculateGroupCenter(group.colors);
                foundGroup = true;
                break;
            }
        }

        // إنشاء مجموعة جديدة
        if (!foundGroup) {
            groups.push({
                center: {r: color.r, g: color.g, b: color.b},
                colors: [color]
            });
        }
    }

    // ترتيب المجموعات حسب الحجم
    groups.sort(function(a, b) {
        return b.colors.length - a.colors.length;
    });

    // أخذ أكبر المجموعات
    var result = [];
    for (var i = 0; i < Math.min(targetCount, groups.length); i++) {
        var group = groups[i];
        result.push({
            r: Math.round(group.center.r),
            g: Math.round(group.center.g),
            b: Math.round(group.center.b),
            frequency: group.colors.length
        });
    }

    return result;
}

// حساب المسافة بين لونين
function colorDistance(color1, color2) {
    var dr = color1.r - color2.r;
    var dg = color1.g - color2.g;
    var db = color1.b - color2.b;
    return Math.sqrt(dr * dr + dg * dg + db * db);
}

// حساب مركز المجموعة
function calculateGroupCenter(colors) {
    var totalR = 0, totalG = 0, totalB = 0;

    for (var i = 0; i < colors.length; i++) {
        totalR += colors[i].r;
        totalG += colors[i].g;
        totalB += colors[i].b;
    }

    return {
        r: totalR / colors.length,
        g: totalG / colors.length,
        b: totalB / colors.length
    };
}

// استخراج الألوان من Illustrator (مبسط)
function extractColorsFromIllustrator(colorCount) {
    try {
        // استخدام نفس الألوان المستوحاة من الطبيعة
        var designInspiredColors = [
            {r: 41, g: 128, b: 185, name: "أزرق تصميم"},     // أزرق
            {r: 192, g: 57, b: 43, name: "أحمر جرافيك"},     // أحمر
            {r: 39, g: 174, b: 96, name: "أخضر إبداعي"},    // أخضر
            {r: 243, g: 156, b: 18, name: "برتقالي فني"},    // برتقالي
            {r: 142, g: 68, b: 173, name: "بنفسجي راقي"},    // بنفسجي
            {r: 52, g: 152, b: 219, name: "أزرق فاتح"},      // أزرق فاتح
            {r: 46, g: 204, b: 113, name: "أخضر نعناعي"},   // أخضر فاتح
            {r: 155, g: 89, b: 182, name: "وردي أرجواني"}   // وردي
        ];

        var colors = [];

        // اختيار ألوان متنوعة
        for (var i = 0; i < colorCount; i++) {
            var colorIndex = i % designInspiredColors.length;
            var baseColor = designInspiredColors[colorIndex];

            // إضافة تنويع للألوان
            var variation = Math.floor(Math.random() * 30) - 15;

            var finalColor = {
                r: Math.max(0, Math.min(255, baseColor.r + variation)),
                g: Math.max(0, Math.min(255, baseColor.g + variation)),
                b: Math.max(0, Math.min(255, baseColor.b + variation))
            };

            colors.push({
                hex: rgbToHex(finalColor.r, finalColor.g, finalColor.b),
                rgb: finalColor,
                name: "لون مستخرج " + (i + 1),
                frequency: 1
            });
        }

        return colors;

    } catch (error) {
        throw new Error("خطأ في استخراج الألوان: " + error.message);
    }
}

// وظائف استخراج الألوان المعقدة مُزالة للتبسيط

// توليد لوحة ألوان متقدمة
function generateAdvancedColorPalette(baseColor, harmonyType, colorCount) {
    var colors = [];

    // التحقق من الألوان التراثية
    if (harmonyType === "arabic_traditional") {
        return generateArabicTraditionalPalette(colorCount);
    } else if (harmonyType === "ramadan") {
        return generateRamadanPalette(colorCount);
    } else if (harmonyType === "eid") {
        return generateEidPalette(colorCount);
    } else if (harmonyType === "image_extraction") {
        return extractColorsFromImage(colorCount);
    }

    // توليد عادي للأنواع الأخرى
    var baseRgb = hexToRgb(baseColor);
    if (!baseRgb) throw new Error("كود اللون غير صحيح");

    var baseHsl = rgbToHsl(baseRgb.r, baseRgb.g, baseRgb.b);

    // إضافة اللون الأساسي
    colors.push({
        hex: baseColor.toUpperCase(),
        rgb: baseRgb,
        name: "اللون الأساسي"
    });

    // توليد الألوان حسب النوع
    switch (harmonyType) {
        case "complementary":
            var compHue = (baseHsl.h + 180) % 360;
            var compRgb = hslToRgb(compHue, baseHsl.s, baseHsl.l);
            colors.push({
                hex: rgbToHex(compRgb.r, compRgb.g, compRgb.b),
                rgb: compRgb,
                name: "مكمل"
            });

            // إضافة تدرجات محسنة
            for (var i = 2; i < colorCount; i++) {
                var factor = (i - 1) / (colorCount - 2);
                var lightness = Math.max(15, Math.min(85, baseHsl.l + (factor - 0.5) * 50));
                var saturation = Math.max(20, Math.min(100, baseHsl.s * (0.7 + factor * 0.6)));
                var rgb = hslToRgb(baseHsl.h, saturation, lightness);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "تدرج " + i
                });
            }
            break;

        case "analogous":
            var angleStep = 25;
            for (var i = 1; i < colorCount; i++) {
                var hue = (baseHsl.h + (i * angleStep)) % 360;
                var lightness = Math.max(20, Math.min(80, baseHsl.l + (Math.random() - 0.5) * 30));
                var saturation = Math.max(30, Math.min(100, baseHsl.s + (Math.random() - 0.5) * 20));
                var rgb = hslToRgb(hue, saturation, lightness);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "متجاور " + i
                });
            }
            break;

        case "triadic":
            var triadicHues = [baseHsl.h, (baseHsl.h + 120) % 360, (baseHsl.h + 240) % 360];
            for (var i = 1; i < Math.min(3, colorCount); i++) {
                var hue = triadicHues[i];
                var rgb = hslToRgb(hue, baseHsl.s, baseHsl.l);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "ثلاثي " + i
                });
            }

            // إضافة تدرجات إضافية
            for (var i = 3; i < colorCount; i++) {
                var baseIndex = i % 3;
                var hue = triadicHues[baseIndex];
                var lightness = Math.max(15, Math.min(85, baseHsl.l + (Math.random() - 0.5) * 40));
                var saturation = Math.max(25, Math.min(95, baseHsl.s * (0.8 + Math.random() * 0.4)));
                var rgb = hslToRgb(hue, saturation, lightness);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "تدرج " + i
                });
            }
            break;

        case "monochromatic":
            for (var i = 1; i < colorCount; i++) {
                var factor = i / (colorCount - 1);
                var lightness = 15 + (factor * 70);
                var saturation = Math.max(10, Math.min(100, baseHsl.s + (Math.random() - 0.5) * 30));
                var rgb = hslToRgb(baseHsl.h, saturation, lightness);
                colors.push({
                    hex: rgbToHex(rgb.r, rgb.g, rgb.b),
                    rgb: rgb,
                    name: "تدرج " + i
                });
            }
            break;

        default:
            // افتراضي - مكمل
            var compHue = (baseHsl.h + 180) % 360;
            var compRgb = hslToRgb(compHue, baseHsl.s, baseHsl.l);
            colors.push({
                hex: rgbToHex(compRgb.r, compRgb.g, compRgb.b),
                rgb: compRgb,
                name: "مكمل"
            });
    }

    return colors.slice(0, colorCount);
}

// توليد لوحة ألوان تراثية عربية
function generateArabicTraditionalPalette(colorCount) {
    var colors = [];
    var traditionalColors = arabicColorSets.traditional;

    for (var i = 0; i < Math.min(colorCount, traditionalColors.length); i++) {
        var color = traditionalColors[i];
        var rgb = hexToRgb(color.hex);
        colors.push({
            hex: color.hex,
            rgb: rgb,
            name: color.name
        });
    }

    // إضافة تدرجات إذا كان العدد المطلوب أكبر
    while (colors.length < colorCount) {
        var baseColor = traditionalColors[colors.length % traditionalColors.length];
        var baseRgb = hexToRgb(baseColor.hex);
        var baseHsl = rgbToHsl(baseRgb.r, baseRgb.g, baseRgb.b);

        // تدرج أفتح أو أغمق
        var lightness = Math.max(10, Math.min(90, baseHsl.l + (Math.random() - 0.5) * 40));
        var rgb = hslToRgb(baseHsl.h, baseHsl.s * 0.8, lightness);

        colors.push({
            hex: rgbToHex(rgb.r, rgb.g, rgb.b),
            rgb: rgb,
            name: "تدرج " + baseColor.name
        });
    }

    return colors;
}

// توليد لوحة ألوان رمضان
function generateRamadanPalette(colorCount) {
    var colors = [];
    var ramadanColors = arabicColorSets.ramadan;

    for (var i = 0; i < Math.min(colorCount, ramadanColors.length); i++) {
        var color = ramadanColors[i];
        var rgb = hexToRgb(color.hex);
        colors.push({
            hex: color.hex,
            rgb: rgb,
            name: color.name
        });
    }

    // إضافة تدرجات ليلية
    while (colors.length < colorCount) {
        var baseIndex = colors.length % ramadanColors.length;
        var baseColor = ramadanColors[baseIndex];
        var baseRgb = hexToRgb(baseColor.hex);
        var baseHsl = rgbToHsl(baseRgb.r, baseRgb.g, baseRgb.b);

        // تدرجات داكنة مناسبة لرمضان
        var lightness = Math.max(5, Math.min(60, baseHsl.l + (Math.random() - 0.7) * 30));
        var rgb = hslToRgb(baseHsl.h, baseHsl.s, lightness);

        colors.push({
            hex: rgbToHex(rgb.r, rgb.g, rgb.b),
            rgb: rgb,
            name: "ليلي " + (colors.length - ramadanColors.length + 1)
        });
    }

    return colors;
}

// توليد لوحة ألوان العيد
function generateEidPalette(colorCount) {
    var colors = [];
    var eidColors = arabicColorSets.eid;

    for (var i = 0; i < Math.min(colorCount, eidColors.length); i++) {
        var color = eidColors[i];
        var rgb = hexToRgb(color.hex);
        colors.push({
            hex: color.hex,
            rgb: rgb,
            name: color.name
        });
    }

    // إضافة ألوان احتفالية زاهية
    while (colors.length < colorCount) {
        var baseIndex = colors.length % eidColors.length;
        var baseColor = eidColors[baseIndex];
        var baseRgb = hexToRgb(baseColor.hex);
        var baseHsl = rgbToHsl(baseRgb.r, baseRgb.g, baseRgb.b);

        // ألوان زاهية ومفرحة
        var lightness = Math.max(40, Math.min(90, baseHsl.l + (Math.random() - 0.3) * 30));
        var saturation = Math.max(60, Math.min(100, baseHsl.s + (Math.random() - 0.2) * 20));
        var rgb = hslToRgb(baseHsl.h, saturation, lightness);

        colors.push({
            hex: rgbToHex(rgb.r, rgb.g, rgb.b),
            rgb: rgb,
            name: "احتفالي " + (colors.length - eidColors.length + 1)
        });
    }

    return colors;
}

// رسم الأشكال المتقدمة
function drawAdvancedColorShapes(palette, settings) {
    try {
        if (isPhotoshop) {
            drawAdvancedShapesPhotoshop(palette, settings);
        } else if (isIllustrator) {
            drawAdvancedShapesIllustrator(palette, settings);
        }

        var message = "تم رسم لوحة الألوان بنجاح!\n\n";
        message += "عدد الألوان: " + palette.length + "\n";
        message += "الشكل: " + getShapeName(settings.shape) + "\n";
        message += "التخطيط: " + getLayoutName(settings.layout) + "\n";
        message += "أشكال بسيطة ونظيفة (مربع أو دائرة فقط)";

        alert(message);

    } catch (error) {
        alert("خطأ في رسم الألوان: " + error.message);
    }
}

// رسم الأشكال في Photoshop
function drawAdvancedShapesPhotoshop(palette, settings) {
    var doc = app.activeDocument;
    var layerSet = doc.layerSets.add();
    layerSet.name = "لوحة الألوان المتقدمة - " + new Date().toLocaleTimeString();

    var positions = calculatePositions(palette.length, settings);

    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var pos = positions[i];

        // إنشاء الشكل حسب النوع (مبسط)
        if (settings.shape === "circle") {
            drawCirclePhotoshop(layerSet, color, pos, settings);
        } else {
            // افتراضي - مربع
            drawSquarePhotoshop(layerSet, color, pos, settings);
        }
    }
}

// رسم الأشكال في Illustrator
function drawAdvancedShapesIllustrator(palette, settings) {
    var doc = app.activeDocument;
    var positions = calculatePositions(palette.length, settings);

    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var pos = positions[i];

        // إنشاء الشكل حسب النوع (مبسط)
        if (settings.shape === "circle") {
            drawCircleIllustrator(doc, color, pos, settings);
        } else {
            // افتراضي - مربع
            drawSquareIllustrator(doc, color, pos, settings);
        }
    }
}

// حساب مواضع الأشكال حسب التخطيط
function calculatePositions(count, settings) {
    var positions = [];
    var startX = 100;
    var startY = isPhotoshop ? 100 : -100;
    var size = settings.size;
    var spacing = settings.spacing;

    switch (settings.layout) {
        case "horizontal":
            for (var i = 0; i < count; i++) {
                positions.push({
                    x: startX + (i * (size + spacing)),
                    y: startY
                });
            }
            break;

        case "vertical":
            for (var i = 0; i < count; i++) {
                positions.push({
                    x: startX,
                    y: startY + (i * (size + spacing) * (isPhotoshop ? 1 : -1))
                });
            }
            break;

        case "grid":
            var cols = Math.ceil(Math.sqrt(count));
            for (var i = 0; i < count; i++) {
                var row = Math.floor(i / cols);
                var col = i % cols;
                positions.push({
                    x: startX + (col * (size + spacing)),
                    y: startY + (row * (size + spacing) * (isPhotoshop ? 1 : -1))
                });
            }
            break;

        case "circle":
            var radius = (count * size) / (2 * Math.PI) + 50;
            var centerX = startX + radius;
            var centerY = startY + (isPhotoshop ? radius : -radius);

            for (var i = 0; i < count; i++) {
                var angle = (i / count) * 2 * Math.PI;
                positions.push({
                    x: centerX + Math.cos(angle) * radius,
                    y: centerY + Math.sin(angle) * radius * (isPhotoshop ? 1 : -1)
                });
            }
            break;

        case "wave":
            var amplitude = 50;
            var frequency = 2;
            for (var i = 0; i < count; i++) {
                var x = startX + (i * (size + spacing));
                var waveY = Math.sin((i / count) * frequency * Math.PI) * amplitude;
                positions.push({
                    x: x,
                    y: startY + waveY * (isPhotoshop ? 1 : -1)
                });
            }
            break;

        case "spiral":
            var spiralRadius = 20;
            var spiralGrowth = 10;
            var centerX = startX + 200;
            var centerY = startY + (isPhotoshop ? 200 : -200);

            for (var i = 0; i < count; i++) {
                var angle = (i / count) * 4 * Math.PI;
                var radius = spiralRadius + (i * spiralGrowth);
                positions.push({
                    x: centerX + Math.cos(angle) * radius,
                    y: centerY + Math.sin(angle) * radius * (isPhotoshop ? 1 : -1)
                });
            }
            break;

        default:
            // افتراضي - أفقي
            for (var i = 0; i < count; i++) {
                positions.push({
                    x: startX + (i * (size + spacing)),
                    y: startY
                });
            }
    }

    return positions;
}

// وظائف رسم الأشكال في Photoshop
function drawSquarePhotoshop(layerSet, color, pos, settings) {
    var doc = app.activeDocument;
    var size = settings.size;

    var bounds = [
        [pos.x, pos.y],
        [pos.x + size, pos.y],
        [pos.x + size, pos.y + size],
        [pos.x, pos.y + size]
    ];

    var layer = layerSet.artLayers.add();
    layer.name = color.name + " - " + color.hex;

    doc.selection.select(bounds);

    var fillColor = new SolidColor();
    fillColor.rgb.red = color.rgb.r;
    fillColor.rgb.green = color.rgb.g;
    fillColor.rgb.blue = color.rgb.b;

    doc.selection.fill(fillColor);

    // التأثيرات مُزالة للحصول على أشكال نظيفة

    doc.selection.deselect();
}

function drawCirclePhotoshop(layerSet, color, pos, settings) {
    var doc = app.activeDocument;
    var size = settings.size;
    var radius = size / 2;

    // إنشاء دائرة باستخدام Ellipse Tool
    var bounds = [
        [pos.x, pos.y],
        [pos.x + size, pos.y],
        [pos.x + size, pos.y + size],
        [pos.x, pos.y + size]
    ];

    var layer = layerSet.artLayers.add();
    layer.name = color.name + " - " + color.hex + " (دائرة)";

    doc.selection.select(bounds, SelectionType.REPLACE, radius, true);

    var fillColor = new SolidColor();
    fillColor.rgb.red = color.rgb.r;
    fillColor.rgb.green = color.rgb.g;
    fillColor.rgb.blue = color.rgb.b;

    doc.selection.fill(fillColor);

    // التأثيرات مُزالة للحصول على أشكال نظيفة

    doc.selection.deselect();
}

// وظائف الأشكال المعقدة مُزالة للتبسيط

// المزيد من وظائف الأشكال المعقدة مُزالة

// جميع وظائف الأشكال المعقدة مُزالة (نجمة، معين، قلب، قطرة) للتبسيط

// التأثيرات البصرية مُزالة للحصول على أشكال نظيفة

// وظائف مساعدة للحصول على أسماء العناصر
function getShapeName(shapeKey) {
    for (var i = 0; i < colorShapes.length; i++) {
        if (colorShapes[i].key === shapeKey) {
            return colorShapes[i].name;
        }
    }
    return "مربع";
}

function getEffectName(effectKey) {
    for (var i = 0; i < visualEffects.length; i++) {
        if (visualEffects[i].key === effectKey) {
            return visualEffects[i].name;
        }
    }
    return "بدون تأثير";
}

function getLayoutName(layoutKey) {
    for (var i = 0; i < layoutTypes.length; i++) {
        if (layoutTypes[i].key === layoutKey) {
            return layoutTypes[i].name;
        }
    }
    return "أفقي";
}

// وظائف رسم مبسطة في Illustrator (سأضيفها في الجزء التالي)
function drawSquareIllustrator(doc, color, pos, settings) {
    var rect = doc.pathItems.rectangle();
    rect.top = pos.y;
    rect.left = pos.x;
    rect.width = settings.size;
    rect.height = settings.size;

    var fillColor = new RGBColor();
    fillColor.red = color.rgb.r;
    fillColor.green = color.rgb.g;
    fillColor.blue = color.rgb.b;

    rect.fillColor = fillColor;
    rect.filled = true;
    rect.stroked = false;
    rect.name = color.name + " - " + color.hex;

    // التأثيرات مُزالة
}

function drawCircleIllustrator(doc, color, pos, settings) {
    var circle = doc.pathItems.ellipse();
    circle.top = pos.y;
    circle.left = pos.x;
    circle.width = settings.size;
    circle.height = settings.size;

    var fillColor = new RGBColor();
    fillColor.red = color.rgb.r;
    fillColor.green = color.rgb.g;
    fillColor.blue = color.rgb.b;

    circle.fillColor = fillColor;
    circle.filled = true;
    circle.stroked = false;
    circle.name = color.name + " - " + color.hex + " (دائرة)";

    // التأثيرات مُزالة
}

// وظائف الأشكال المعقدة في Illustrator مُزالة للتبسيط

// جميع وظائف الأشكال المعقدة في Illustrator مُزالة

// التأثيرات البصرية مُزالة للحصول على أشكال نظيفة

// وظائف التصدير المتقدمة
function exportPalette(palette, format, analysis) {
    try {
        var file = File.saveDialog("حفظ لوحة الألوان", "*." + format);
        if (!file) return;

        switch (format) {
            case "css":
                exportAsCSS(palette, file, analysis);
                break;
            case "scss":
                exportAsSCSS(palette, file, analysis);
                break;
            case "json":
                exportAsAdvancedJSON(palette, file, analysis);
                break;
            case "txt":
                exportAsText(palette, file, analysis);
                break;
            case "html":
                exportAsHTML(palette, file, analysis);
                break;
            default:
                alert("صيغة غير مدعومة: " + format);
        }

        alert("تم تصدير لوحة الألوان بنجاح إلى:\n" + file.fsName);

    } catch (error) {
        alert("خطأ في التصدير: " + error.message);
    }
}

// تصدير كـ CSS
function exportAsCSS(palette, file, analysis) {
    var cssContent = "/* لوحة الألوان - تم إنشاؤها بواسطة مولد لوحات الألوان المتقدم */\n";
    cssContent += "/* تاريخ الإنشاء: " + new Date().toLocaleDateString() + " */\n";
    cssContent += "/* تحليل اللوحة: " + analysis.temperature + " - " + analysis.harmony + " - " + analysis.accessibility + " */\n\n";

    cssContent += ":root {\n";

    // متغيرات CSS للألوان
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var varName = color.name.replace(/\s+/g, '-').toLowerCase();
        cssContent += "  --color-" + (i + 1) + "-" + varName + ": " + color.hex + ";\n";
        cssContent += "  --color-" + (i + 1) + "-rgb: " + color.rgb.r + ", " + color.rgb.g + ", " + color.rgb.b + ";\n";
    }

    cssContent += "}\n\n";

    // فئات CSS للألوان
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var className = color.name.replace(/\s+/g, '-').toLowerCase();

        cssContent += ".bg-" + className + " {\n";
        cssContent += "  background-color: " + color.hex + ";\n";
        cssContent += "}\n\n";

        cssContent += ".text-" + className + " {\n";
        cssContent += "  color: " + color.hex + ";\n";
        cssContent += "}\n\n";

        cssContent += ".border-" + className + " {\n";
        cssContent += "  border-color: " + color.hex + ";\n";
        cssContent += "}\n\n";
    }

    // إضافة تركيبات ألوان مقترحة
    cssContent += "/* تركيبات ألوان مقترحة */\n";
    for (var i = 0; i < analysis.contrast.length; i++) {
        var contrast = analysis.contrast[i];
        if (contrast.ratio >= 4.5) {
            cssContent += "/* " + contrast.color1 + " على " + contrast.color2 + " - تباين: " + contrast.ratio + " */\n";
        }
    }

    file.open("w");
    file.write(cssContent);
    file.close();
}

// تصدير كـ SCSS
function exportAsSCSS(palette, file, analysis) {
    var scssContent = "// لوحة الألوان - SCSS Variables\n";
    scssContent += "// تاريخ الإنشاء: " + new Date().toLocaleDateString() + "\n";
    scssContent += "// تحليل: " + analysis.temperature + " - " + analysis.accessibility + "\n\n";

    // متغيرات SCSS
    scssContent += "// الألوان الأساسية\n";
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var varName = color.name.replace(/\s+/g, '-').toLowerCase();
        scssContent += "$color-" + (i + 1) + "-" + varName + ": " + color.hex + ";\n";
    }

    scssContent += "\n// خريطة الألوان\n";
    scssContent += "$colors: (\n";
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var varName = color.name.replace(/\s+/g, '-').toLowerCase();
        scssContent += "  '" + varName + "': " + color.hex;
        if (i < palette.length - 1) scssContent += ",";
        scssContent += "\n";
    }
    scssContent += ");\n\n";

    // دوال SCSS مساعدة
    scssContent += "// دالة للحصول على لون\n";
    scssContent += "@function get-color($name) {\n";
    scssContent += "  @return map-get($colors, $name);\n";
    scssContent += "}\n\n";

    scssContent += "// Mixin للخلفيات\n";
    scssContent += "@mixin bg-color($name) {\n";
    scssContent += "  background-color: get-color($name);\n";
    scssContent += "}\n\n";

    scssContent += "// Mixin للنصوص\n";
    scssContent += "@mixin text-color($name) {\n";
    scssContent += "  color: get-color($name);\n";
    scssContent += "}\n";

    file.open("w");
    file.write(scssContent);
    file.close();
}

// تصدير JSON متقدم
function exportAsAdvancedJSON(palette, file, analysis) {
    var jsonContent = "{\n";
    jsonContent += '  "metadata": {\n';
    jsonContent += '    "name": "لوحة الألوان المتقدمة",\n';
    jsonContent += '    "createdAt": "' + new Date().toISOString() + '",\n';
    jsonContent += '    "version": "2.0.0",\n';
    jsonContent += '    "generator": "Color Palette Generator Advanced"\n';
    jsonContent += '  },\n';

    jsonContent += '  "analysis": {\n';
    jsonContent += '    "temperature": "' + analysis.temperature + '",\n';
    jsonContent += '    "harmony": "' + analysis.harmony + '",\n';
    jsonContent += '    "accessibility": "' + analysis.accessibility + '",\n';
    jsonContent += '    "averageHue": ' + analysis.averageHue + ',\n';
    jsonContent += '    "averageSaturation": ' + analysis.averageSaturation + ',\n';
    jsonContent += '    "averageLightness": ' + analysis.averageLightness + '\n';
    jsonContent += '  },\n';

    jsonContent += '  "colors": [\n';
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var hsl = rgbToHsl(color.rgb.r, color.rgb.g, color.rgb.b);

        jsonContent += '    {\n';
        jsonContent += '      "id": ' + (i + 1) + ',\n';
        jsonContent += '      "name": "' + color.name + '",\n';
        jsonContent += '      "hex": "' + color.hex + '",\n';
        jsonContent += '      "rgb": {\n';
        jsonContent += '        "r": ' + color.rgb.r + ',\n';
        jsonContent += '        "g": ' + color.rgb.g + ',\n';
        jsonContent += '        "b": ' + color.rgb.b + '\n';
        jsonContent += '      },\n';
        jsonContent += '      "hsl": {\n';
        jsonContent += '        "h": ' + Math.round(hsl.h) + ',\n';
        jsonContent += '        "s": ' + Math.round(hsl.s) + ',\n';
        jsonContent += '        "l": ' + Math.round(hsl.l) + '\n';
        jsonContent += '      }\n';
        jsonContent += '    }';

        if (i < palette.length - 1) {
            jsonContent += ',';
        }
        jsonContent += '\n';
    }
    jsonContent += '  ],\n';

    jsonContent += '  "contrast": [\n';
    for (var i = 0; i < analysis.contrast.length; i++) {
        var contrast = analysis.contrast[i];
        jsonContent += '    {\n';
        jsonContent += '      "color1": "' + contrast.color1 + '",\n';
        jsonContent += '      "color2": "' + contrast.color2 + '",\n';
        jsonContent += '      "ratio": ' + contrast.ratio + ',\n';
        jsonContent += '      "wcagAA": ' + (contrast.ratio >= 4.5 ? 'true' : 'false') + ',\n';
        jsonContent += '      "wcagAAA": ' + (contrast.ratio >= 7 ? 'true' : 'false') + '\n';
        jsonContent += '    }';

        if (i < analysis.contrast.length - 1) {
            jsonContent += ',';
        }
        jsonContent += '\n';
    }
    jsonContent += '  ]\n';
    jsonContent += '}';

    file.open("w");
    file.write(jsonContent);
    file.close();
}

// تصدير كـ HTML
function exportAsHTML(palette, file, analysis) {
    var htmlContent = '<!DOCTYPE html>\n';
    htmlContent += '<html lang="ar" dir="rtl">\n';
    htmlContent += '<head>\n';
    htmlContent += '    <meta charset="UTF-8">\n';
    htmlContent += '    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n';
    htmlContent += '    <title>لوحة الألوان المتقدمة</title>\n';
    htmlContent += '    <style>\n';
    htmlContent += '        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }\n';
    htmlContent += '        .palette { display: flex; flex-wrap: wrap; gap: 20px; margin: 20px 0; }\n';
    htmlContent += '        .color-card { background: white; border-radius: 8px; padding: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }\n';
    htmlContent += '        .color-preview { width: 100px; height: 100px; border-radius: 8px; margin-bottom: 10px; }\n';
    htmlContent += '        .color-info { font-size: 14px; }\n';
    htmlContent += '        .analysis { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }\n';
    htmlContent += '        .header { text-align: center; color: #333; }\n';
    htmlContent += '    </style>\n';
    htmlContent += '</head>\n';
    htmlContent += '<body>\n';

    htmlContent += '    <div class="header">\n';
    htmlContent += '        <h1>لوحة الألوان المتقدمة</h1>\n';
    htmlContent += '        <p>تم إنشاؤها في: ' + new Date().toLocaleDateString() + '</p>\n';
    htmlContent += '    </div>\n';

    htmlContent += '    <div class="analysis">\n';
    htmlContent += '        <h2>تحليل اللوحة</h2>\n';
    htmlContent += '        <p><strong>حرارة الألوان:</strong> ' + analysis.temperature + '</p>\n';
    htmlContent += '        <p><strong>التناغم:</strong> ' + analysis.harmony + '</p>\n';
    htmlContent += '        <p><strong>إمكانية الوصول:</strong> ' + analysis.accessibility + '</p>\n';
    htmlContent += '        <p><strong>متوسط الصبغة:</strong> ' + analysis.averageHue + '°</p>\n';
    htmlContent += '        <p><strong>متوسط التشبع:</strong> ' + analysis.averageSaturation + '%</p>\n';
    htmlContent += '        <p><strong>متوسط الإضاءة:</strong> ' + analysis.averageLightness + '%</p>\n';
    htmlContent += '    </div>\n';

    htmlContent += '    <div class="palette">\n';
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var hsl = rgbToHsl(color.rgb.r, color.rgb.g, color.rgb.b);

        htmlContent += '        <div class="color-card">\n';
        htmlContent += '            <div class="color-preview" style="background-color: ' + color.hex + ';"></div>\n';
        htmlContent += '            <div class="color-info">\n';
        htmlContent += '                <strong>' + color.name + '</strong><br>\n';
        htmlContent += '                HEX: ' + color.hex + '<br>\n';
        htmlContent += '                RGB: ' + color.rgb.r + ', ' + color.rgb.g + ', ' + color.rgb.b + '<br>\n';
        htmlContent += '                HSL: ' + Math.round(hsl.h) + '°, ' + Math.round(hsl.s) + '%, ' + Math.round(hsl.l) + '%\n';
        htmlContent += '            </div>\n';
        htmlContent += '        </div>\n';
    }
    htmlContent += '    </div>\n';

    htmlContent += '</body>\n';
    htmlContent += '</html>';

    file.open("w");
    file.write(htmlContent);
    file.close();
}

// تصدير كـ نص
function exportAsText(palette, file, analysis) {
    var textContent = "=== لوحة الألوان المتقدمة ===\n";
    textContent += "تاريخ الإنشاء: " + new Date().toLocaleDateString() + "\n";
    textContent += "الوقت: " + new Date().toLocaleTimeString() + "\n\n";

    textContent += "=== تحليل اللوحة ===\n";
    textContent += "حرارة الألوان: " + analysis.temperature + "\n";
    textContent += "التناغم: " + analysis.harmony + "\n";
    textContent += "إمكانية الوصول: " + analysis.accessibility + "\n";
    textContent += "متوسط الصبغة: " + analysis.averageHue + "°\n";
    textContent += "متوسط التشبع: " + analysis.averageSaturation + "%\n";
    textContent += "متوسط الإضاءة: " + analysis.averageLightness + "%\n\n";

    textContent += "=== الألوان ===\n";
    for (var i = 0; i < palette.length; i++) {
        var color = palette[i];
        var hsl = rgbToHsl(color.rgb.r, color.rgb.g, color.rgb.b);

        textContent += (i + 1) + ". " + color.name + "\n";
        textContent += "   HEX: " + color.hex + "\n";
        textContent += "   RGB: " + color.rgb.r + ", " + color.rgb.g + ", " + color.rgb.b + "\n";
        textContent += "   HSL: " + Math.round(hsl.h) + "°, " + Math.round(hsl.s) + "%, " + Math.round(hsl.l) + "%\n\n";
    }

    textContent += "=== تحليل التباين ===\n";
    for (var i = 0; i < analysis.contrast.length; i++) {
        var contrast = analysis.contrast[i];
        textContent += contrast.color1 + " مع " + contrast.color2 + ": " + contrast.ratio + "\n";
        if (contrast.ratio >= 7) {
            textContent += "   ✓ ممتاز (WCAG AAA)\n";
        } else if (contrast.ratio >= 4.5) {
            textContent += "   ✓ جيد (WCAG AA)\n";
        } else {
            textContent += "   ✗ ضعيف\n";
        }
    }

    file.open("w");
    file.write(textContent);
    file.close();
}

// إنشاء واجهة المستخدم المتقدمة
function createAdvancedUI() {
    dialog = new Window("dialog", SCRIPT_NAME + " v" + SCRIPT_VERSION);
    dialog.orientation = "column";
    dialog.alignChildren = "fill";
    dialog.spacing = 10;
    dialog.margins = 16;
    dialog.preferredSize.width = 500;

    // علامات التبويب الرئيسية
    var tabPanel = dialog.add("tabbedpanel");
    tabPanel.alignChildren = "fill";
    tabPanel.preferredSize.height = 400;

    // تبويب الإعدادات الأساسية
    var basicTab = tabPanel.add("tab", undefined, "الإعدادات الأساسية");
    basicTab.orientation = "column";
    basicTab.alignChildren = "fill";
    basicTab.spacing = 10;

    // مجموعة اللون الأساسي
    var colorGroup = basicTab.add("panel", undefined, "اللون الأساسي");
    colorGroup.orientation = "column";
    colorGroup.alignChildren = "fill";
    colorGroup.spacing = 8;
    colorGroup.margins = 10;

    var colorInputGroup = colorGroup.add("group");
    colorInputGroup.add("statictext", undefined, "كود اللون:");
    var colorInput = colorInputGroup.add("edittext", undefined, "#6366f1");
    colorInput.characters = 12;
    var randomBtn = colorInputGroup.add("button", undefined, "عشوائي");
    var eyedropperBtn = colorInputGroup.add("button", undefined, "قطارة");

    // إضافة زر استخراج من الصورة
    var extractGroup = colorGroup.add("group");
    var extractBtn = extractGroup.add("button", undefined, "استخراج ألوان من الصورة/الطبقة المحددة");
    extractBtn.fillBrush = extractBtn.graphics.newBrush(extractBtn.graphics.BrushType.SOLID_COLOR, [0.2, 0.6, 0.9, 1]);

    // إضافة ملاحظة
    var imageNote = colorGroup.add("statictext", undefined, "ملاحظة: عند اختيار 'استخراج من الصورة' سيتم تجاهل اللون الأساسي المدخل أعلاه");
    imageNote.graphics.foregroundColor = imageNote.graphics.newPen(imageNote.graphics.PenType.SOLID_COLOR, [0.5, 0.5, 0.5], 1);

    // نوع التناغم
    var harmonyGroup = colorGroup.add("group");
    harmonyGroup.add("statictext", undefined, "نوع التناغم:");
    var harmonyDropdown = harmonyGroup.add("dropdownlist");
    for (var i = 0; i < harmonyTypes.length; i++) {
        harmonyDropdown.add("item", harmonyTypes[i].name);
    }
    harmonyDropdown.selection = 0;

    // عدد الألوان
    var countGroup = colorGroup.add("group");
    countGroup.add("statictext", undefined, "عدد الألوان:");
    var colorCountSlider = countGroup.add("slider", undefined, 5, 3, 8);
    var colorCountText = countGroup.add("statictext", undefined, "5");
    colorCountSlider.onChanging = function() {
        colorCountText.text = Math.round(colorCountSlider.value);
    };

    // تبويب الأشكال والتأثيرات
    var visualTab = tabPanel.add("tab", undefined, "الأشكال والتأثيرات");
    visualTab.orientation = "column";
    visualTab.alignChildren = "fill";
    visualTab.spacing = 10;

    // مجموعة الأشكال
    var shapeGroup = visualTab.add("panel", undefined, "شكل الألوان");
    shapeGroup.orientation = "column";
    shapeGroup.alignChildren = "fill";
    shapeGroup.spacing = 8;
    shapeGroup.margins = 10;

    var shapeDropdownGroup = shapeGroup.add("group");
    shapeDropdownGroup.add("statictext", undefined, "الشكل:");
    var shapeDropdown = shapeDropdownGroup.add("dropdownlist");
    for (var i = 0; i < colorShapes.length; i++) {
        shapeDropdown.add("item", colorShapes[i].name);
    }
    shapeDropdown.selection = 0;

    // حجم الأشكال
    var sizeGroup = shapeGroup.add("group");
    sizeGroup.add("statictext", undefined, "الحجم:");
    var sizeSlider = sizeGroup.add("slider", undefined, 100, 50, 200);
    var sizeText = sizeGroup.add("statictext", undefined, "100px");
    sizeSlider.onChanging = function() {
        sizeText.text = Math.round(sizeSlider.value) + "px";
    };

    // المسافة بين الأشكال
    var spacingGroup = shapeGroup.add("group");
    spacingGroup.add("statictext", undefined, "المسافة:");
    var spacingSlider = spacingGroup.add("slider", undefined, 20, 5, 50);
    var spacingText = spacingGroup.add("statictext", undefined, "20px");
    spacingSlider.onChanging = function() {
        spacingText.text = Math.round(spacingSlider.value) + "px";
    };

    // التأثيرات البصرية مُزالة للحصول على أشكال نظيفة
    var noteGroup = visualTab.add("panel", undefined, "ملاحظة");
    noteGroup.orientation = "column";
    noteGroup.alignChildren = "fill";
    noteGroup.spacing = 8;
    noteGroup.margins = 10;

    noteGroup.add("statictext", undefined, "تم إزالة التأثيرات البصرية للحصول على أشكال نظيفة وبسيطة");

    // مجموعة التخطيط
    var layoutGroup = visualTab.add("panel", undefined, "تخطيط الترتيب");
    layoutGroup.orientation = "column";
    layoutGroup.alignChildren = "fill";
    layoutGroup.spacing = 8;
    layoutGroup.margins = 10;

    var layoutDropdownGroup = layoutGroup.add("group");
    layoutDropdownGroup.add("statictext", undefined, "التخطيط:");
    var layoutDropdown = layoutDropdownGroup.add("dropdownlist");
    for (var i = 0; i < layoutTypes.length; i++) {
        layoutDropdown.add("item", layoutTypes[i].name);
    }
    layoutDropdown.selection = 0;

    // تبويب التحليل والتصدير
    var exportTab = tabPanel.add("tab", undefined, "التحليل والتصدير");
    exportTab.orientation = "column";
    exportTab.alignChildren = "fill";
    exportTab.spacing = 10;

    // مجموعة الإجراءات
    var actionGroup = exportTab.add("panel", undefined, "الإجراءات");
    actionGroup.orientation = "column";
    actionGroup.alignChildren = "fill";
    actionGroup.spacing = 8;
    actionGroup.margins = 10;

    var drawShapesCheck = actionGroup.add("checkbox", undefined, "رسم الأشكال الملونة");
    var addSwatchesCheck = actionGroup.add("checkbox", undefined, "إضافة إلى Swatches");
    var analyzeCheck = actionGroup.add("checkbox", undefined, "تحليل اللوحة");

    drawShapesCheck.value = true;
    addSwatchesCheck.value = true;
    analyzeCheck.value = true;

    // مجموعة التصدير
    var exportGroup = exportTab.add("panel", undefined, "تصدير اللوحة");
    exportGroup.orientation = "column";
    exportGroup.alignChildren = "fill";
    exportGroup.spacing = 8;
    exportGroup.margins = 10;

    var exportFormats = [
        {key: "css", name: "CSS"},
        {key: "scss", name: "SCSS"},
        {key: "json", name: "JSON"},
        {key: "html", name: "HTML"},
        {key: "txt", name: "نص"}
    ];

    var exportChecks = [];
    for (var i = 0; i < exportFormats.length; i++) {
        var check = exportGroup.add("checkbox", undefined, "تصدير كـ " + exportFormats[i].name);
        exportChecks.push({check: check, format: exportFormats[i].key});
    }

    // أزرار التحكم
    var buttonGroup = dialog.add("group");
    buttonGroup.alignment = "center";
    buttonGroup.spacing = 10;

    var previewBtn = buttonGroup.add("button", undefined, "معاينة");
    var generateBtn = buttonGroup.add("button", undefined, "توليد لوحة الألوان");
    var cancelBtn = buttonGroup.add("button", undefined, "إلغاء");

    // أحداث الأزرار
    randomBtn.onClick = function() {
        var colors = ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD", "#F39C12", "#E74C3C", "#9B59B6", "#3498DB"];
        colorInput.text = colors[Math.floor(Math.random() * colors.length)];
    };

    eyedropperBtn.onClick = function() {
        alert("استخدم أداة القطارة في البرنامج لاختيار لون، ثم انسخ الكود هنا");
    };

    extractBtn.onClick = function() {
        try {
            // تعيين نوع التناغم إلى استخراج من الصورة
            for (var i = 0; i < harmonyTypes.length; i++) {
                if (harmonyTypes[i].key === "image_extraction") {
                    harmonyDropdown.selection = i;
                    break;
                }
            }

            // رسالة تأكيد
            var message = "تم تعيين نوع التناغم إلى 'استخراج من الصورة'\n\n";

            if (isPhotoshop) {
                message += "سيتم تحليل الطبقة النشطة أو المستند بالكامل\n";
                message += "تأكد من وجود طبقة محددة أو صورة مفتوحة";
            } else if (isIllustrator) {
                message += "سيتم تحليل العناصر المحددة أو جميع العناصر\n";
                message += "حدد عناصر معينة للحصول على نتائج أفضل";
            }

            alert(message);

        } catch (error) {
            alert("خطأ: " + error.message);
        }
    };

    previewBtn.onClick = function() {
        try {
            var harmonyIndex = harmonyDropdown.selection.index;
            var harmonyKey = harmonyTypes[harmonyIndex].key;
            var colorCount = Math.round(colorCountSlider.value);

            // التحقق من نوع التناغم
            if (harmonyKey === "image_extraction") {
                // معاينة استخراج من الصورة
                var message = "معاينة استخراج الألوان من الصورة:\n\n";

                if (isPhotoshop) {
                    message += "سيتم تحليل: الطبقة النشطة أو المستند\n";
                } else if (isIllustrator) {
                    message += "سيتم تحليل: العناصر المحددة أو جميع العناصر\n";
                }

                message += "عدد الألوان المطلوب: " + colorCount + "\n\n";
                message += "ملاحظة: قد يستغرق التحليل بعض الوقت حسب حجم الصورة";

                alert(message);
                return;
            }

            // معاينة عادية للأنواع الأخرى
            var baseColor = colorInput.text;
            if (!isValidHexColor(baseColor)) {
                alert("كود اللون غير صحيح");
                return;
            }

            var palette = generateAdvancedColorPalette(baseColor, harmonyKey, colorCount);
            var analysis = analyzePalette(palette);

            var message = "معاينة لوحة الألوان:\n\n";
            message += "نوع التناغم: " + harmonyTypes[harmonyIndex].name + "\n";
            message += "عدد الألوان: " + palette.length + "\n\n";
            message += "تحليل اللوحة:\n";
            message += "• حرارة الألوان: " + analysis.temperature + "\n";
            message += "• إمكانية الوصول: " + analysis.accessibility + "\n";
            message += "• متوسط الإضاءة: " + analysis.averageLightness + "%\n\n";
            message += "الألوان:\n";

            for (var i = 0; i < Math.min(5, palette.length); i++) {
                message += "• " + palette[i].name + ": " + palette[i].hex + "\n";
            }

            if (palette.length > 5) {
                message += "... و " + (palette.length - 5) + " ألوان أخرى";
            }

            alert(message);

        } catch (error) {
            alert("خطأ في المعاينة: " + error.message);
        }
    };

    generateBtn.onClick = function() {
        try {
            var harmonyIndex = harmonyDropdown.selection.index;
            var harmonyKey = harmonyTypes[harmonyIndex].key;
            var colorCount = Math.round(colorCountSlider.value);

            // التحقق من نوع التناغم
            if (harmonyKey !== "image_extraction") {
                var baseColor = colorInput.text;
                if (!isValidHexColor(baseColor)) {
                    alert("كود اللون غير صحيح. يرجى استخدام تنسيق HEX صحيح مثل: #FF5733");
                    return;
                }
            } else {
                // للاستخراج من الصورة، لا نحتاج للون أساسي
                var baseColor = "#000000"; // قيمة وهمية
            }

            // إعداد الإعدادات (بدون تأثيرات)
            currentSettings = {
                shape: colorShapes[shapeDropdown.selection.index].key,
                effect: "none", // التأثيرات مُزالة
                layout: layoutTypes[layoutDropdown.selection.index].key,
                size: Math.round(sizeSlider.value),
                spacing: Math.round(spacingSlider.value)
            };

            // توليد اللوحة
            currentPalette = generateAdvancedColorPalette(baseColor, harmonyKey, colorCount);
            var analysis = analyzePalette(currentPalette);

            // تنفيذ الإجراءات
            if (drawShapesCheck.value) {
                drawAdvancedColorShapes(currentPalette, currentSettings);
            }

            if (addSwatchesCheck.value) {
                addColorsToSwatches(currentPalette);
            }

            // التصدير
            for (var i = 0; i < exportChecks.length; i++) {
                if (exportChecks[i].check.value) {
                    exportPalette(currentPalette, exportChecks[i].format, analysis);
                }
            }

            // عرض التحليل
            if (analyzeCheck.value) {
                showAnalysisDialog(analysis);
            }

            dialog.close();

        } catch (error) {
            alert("حدث خطأ: " + error.message);
        }
    };

    cancelBtn.onClick = function() {
        dialog.close();
    };

    return dialog;
}

// عرض نافذة تحليل اللوحة
function showAnalysisDialog(analysis) {
    var analysisDialog = new Window("dialog", "تحليل لوحة الألوان");
    analysisDialog.orientation = "column";
    analysisDialog.alignChildren = "fill";
    analysisDialog.spacing = 10;
    analysisDialog.margins = 16;
    analysisDialog.preferredSize.width = 400;

    // معلومات عامة
    var infoGroup = analysisDialog.add("panel", undefined, "معلومات عامة");
    infoGroup.orientation = "column";
    infoGroup.alignChildren = "fill";
    infoGroup.spacing = 5;
    infoGroup.margins = 10;

    infoGroup.add("statictext", undefined, "حرارة الألوان: " + analysis.temperature);
    infoGroup.add("statictext", undefined, "التناغم: " + analysis.harmony);
    infoGroup.add("statictext", undefined, "إمكانية الوصول: " + analysis.accessibility);
    infoGroup.add("statictext", undefined, "متوسط الصبغة: " + analysis.averageHue + "°");
    infoGroup.add("statictext", undefined, "متوسط التشبع: " + analysis.averageSaturation + "%");
    infoGroup.add("statictext", undefined, "متوسط الإضاءة: " + analysis.averageLightness + "%");

    // تحليل التباين
    var contrastGroup = analysisDialog.add("panel", undefined, "تحليل التباين");
    contrastGroup.orientation = "column";
    contrastGroup.alignChildren = "fill";
    contrastGroup.spacing = 5;
    contrastGroup.margins = 10;
    contrastGroup.preferredSize.height = 150;

    var contrastList = contrastGroup.add("listbox");
    contrastList.preferredSize.height = 120;

    for (var i = 0; i < Math.min(10, analysis.contrast.length); i++) {
        var contrast = analysis.contrast[i];
        var status = "";
        if (contrast.ratio >= 7) {
            status = " ✓ ممتاز";
        } else if (contrast.ratio >= 4.5) {
            status = " ✓ جيد";
        } else {
            status = " ✗ ضعيف";
        }

        var item = contrastList.add("item", contrast.color1 + " مع " + contrast.color2 + ": " + contrast.ratio + status);
    }

    // توصيات
    var recommendGroup = analysisDialog.add("panel", undefined, "التوصيات");
    recommendGroup.orientation = "column";
    recommendGroup.alignChildren = "fill";
    recommendGroup.spacing = 5;
    recommendGroup.margins = 10;

    var recommendations = getRecommendations(analysis);
    for (var i = 0; i < recommendations.length; i++) {
        recommendGroup.add("statictext", undefined, "• " + recommendations[i]);
    }

    // أزرار
    var buttonGroup = analysisDialog.add("group");
    buttonGroup.alignment = "center";
    var okBtn = buttonGroup.add("button", undefined, "موافق");

    okBtn.onClick = function() {
        analysisDialog.close();
    };

    analysisDialog.show();
}

// الحصول على توصيات بناءً على التحليل
function getRecommendations(analysis) {
    var recommendations = [];

    if (analysis.accessibility === "ضعيف") {
        recommendations.push("يُنصح بزيادة التباين بين الألوان لتحسين إمكانية الوصول");
    }

    if (analysis.averageLightness < 30) {
        recommendations.push("الألوان داكنة جداً، قد تحتاج لإضافة ألوان أفتح للتوازن");
    } else if (analysis.averageLightness > 70) {
        recommendations.push("الألوان فاتحة جداً، قد تحتاج لإضافة ألوان أغمق للتوازن");
    }

    if (analysis.averageSaturation < 20) {
        recommendations.push("الألوان باهتة، يمكن زيادة التشبع لمظهر أكثر حيوية");
    } else if (analysis.averageSaturation > 80) {
        recommendations.push("الألوان مشبعة جداً، قد تحتاج لتخفيف التشبع قليلاً");
    }

    if (analysis.temperature === "دافئ") {
        recommendations.push("لوحة دافئة مناسبة للمشاريع الودودة والطاقة الإيجابية");
    } else if (analysis.temperature === "بارد") {
        recommendations.push("لوحة باردة مناسبة للمشاريع المهنية والتقنية");
    }

    if (recommendations.length === 0) {
        recommendations.push("لوحة ألوان متوازنة وجيدة!");
    }

    return recommendations;
}

// إضافة الألوان إلى Swatches (محسنة)
function addColorsToSwatches(palette) {
    try {
        if (isPhotoshop) {
            for (var i = 0; i < palette.length; i++) {
                var color = palette[i];
                var solidColor = new SolidColor();
                solidColor.rgb.red = color.rgb.r;
                solidColor.rgb.green = color.rgb.g;
                solidColor.rgb.blue = color.rgb.b;
                app.foregroundColor = solidColor;
            }
            alert("تم تعيين الألوان. يمكنك إضافتها للـ Swatches يدوياً من Color panel");
        } else if (isIllustrator) {
            var doc = app.activeDocument;
            for (var i = 0; i < palette.length; i++) {
                var color = palette[i];

                try {
                    var newColor = doc.colors.add();
                    newColor.name = color.name + " - " + color.hex;

                    var rgbColor = new RGBColor();
                    rgbColor.red = color.rgb.r;
                    rgbColor.green = color.rgb.g;
                    rgbColor.blue = color.rgb.b;
                    newColor.color = rgbColor;

                    var newSwatch = doc.swatches.add();
                    newSwatch.name = color.name;
                    newSwatch.color = newColor;
                } catch (e) {
                    // تجاهل أخطاء إضافة الألوان المكررة
                }
            }
            alert("تم إضافة " + palette.length + " لون إلى Swatches بنجاح!");
        }
    } catch (error) {
        alert("خطأ في إضافة الألوان للـ Swatches: " + error.message);
    }
}

// الوظيفة الرئيسية
function main() {
    try {
        // التحقق من وجود مستند مفتوح
        if (app.documents.length == 0) {
            var createDoc = confirm("لا يوجد مستند مفتوح. هل تريد إنشاء مستند جديد؟");
            if (createDoc) {
                if (isPhotoshop) {
                    app.documents.add(1200, 800, 72, "Color Palette Advanced Workspace", NewDocumentMode.RGB);
                } else if (isIllustrator) {
                    var doc = app.documents.add(DocumentColorSpace.RGB, 1200, 800);
                    doc.name = "Color Palette Advanced Workspace";
                }
            } else {
                return;
            }
        }

        // عرض رسالة ترحيب
        var welcomeMessage = "مرحباً بك في مولد لوحات الألوان المتقدم! 🎨\n\n";
        welcomeMessage += "البرنامج: " + app.name + "\n";
        welcomeMessage += "الإصدار: " + SCRIPT_VERSION + "\n\n";
        welcomeMessage += "المميزات:\n";
        welcomeMessage += "🔲 شكلان بسيطان (مربع ودائرة)\n";
        welcomeMessage += "🎨 أشكال نظيفة بدون تأثيرات\n";
        welcomeMessage += "📊 تحليل شامل للألوان\n";
        welcomeMessage += "🖼️ استخراج ألوان من الصور\n";
        welcomeMessage += "🌍 ألوان تراثية عربية\n";
        welcomeMessage += "💾 تصدير متعدد الصيغ\n";
        welcomeMessage += "🎯 6 تخطيطات ترتيب\n\n";
        welcomeMessage += "هل تريد المتابعة؟";

        if (!confirm(welcomeMessage)) {
            return;
        }

        // إنشاء وعرض واجهة المستخدم المتقدمة
        var ui = createAdvancedUI();
        ui.show();

    } catch (error) {
        alert("خطأ في تشغيل السكريبت: " + error.message + "\n\nيرجى التأكد من:\n• وجود مستند مفتوح\n• توافق إصدار البرنامج\n• صلاحيات تشغيل السكريبت");
    }
}

// تشغيل السكريبت
main();
