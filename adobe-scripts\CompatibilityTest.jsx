/*
 * Compatibility Test Script
 * سكريبت اختبار التوافق
 * 
 * يختبر جميع وظائف مولد لوحات الألوان في Photoshop و Illustrator
 */

// التحقق من البرنامج المستخدم
var isPhotoshop = (app.name.indexOf("Photoshop") >= 0);
var isIllustrator = (app.name.indexOf("Illustrator") >= 0);

function runCompatibilityTest() {
    var testResults = {
        appName: app.name,
        appVersion: app.version,
        tests: [],
        passed: 0,
        failed: 0
    };
    
    try {
        // اختبار 1: التحقق من وجود مستند
        testResults.tests.push(testDocumentExists());
        
        // اختبار 2: اختبار وظائف الألوان الأساسية
        testResults.tests.push(testColorFunctions());
        
        // اختبار 3: اختبار توليد لوحات الألوان
        testResults.tests.push(testPaletteGeneration());
        
        // اختبار 4: اختبار رسم الأشكال
        testResults.tests.push(testShapeDrawing());
        
        // اختبار 5: اختبار إضافة Swatches
        testResults.tests.push(testSwatchesAddition());
        
        // اختبار 6: اختبار التصدير
        testResults.tests.push(testExportFunctions());
        
        // اختبار خاص بـ Photoshop
        if (isPhotoshop) {
            testResults.tests.push(testPhotoshopSpecific());
        }
        
        // اختبار خاص بـ Illustrator
        if (isIllustrator) {
            testResults.tests.push(testIllustratorSpecific());
        }
        
        // حساب النتائج
        for (var i = 0; i < testResults.tests.length; i++) {
            if (testResults.tests[i].passed) {
                testResults.passed++;
            } else {
                testResults.failed++;
            }
        }
        
        // عرض النتائج
        displayTestResults(testResults);
        
    } catch (error) {
        alert("خطأ في تشغيل الاختبارات: " + error.message);
    }
}

// اختبار وجود مستند
function testDocumentExists() {
    var test = {
        name: "فحص وجود مستند",
        passed: false,
        message: ""
    };
    
    try {
        if (app.documents.length > 0) {
            test.passed = true;
            test.message = "يوجد " + app.documents.length + " مستند مفتوح";
        } else {
            test.message = "لا يوجد مستند مفتوح - سيتم إنشاء مستند تجريبي";
            
            // إنشاء مستند تجريبي
            if (isPhotoshop) {
                app.documents.add(800, 600, 72, "Test Document", NewDocumentMode.RGB);
            } else if (isIllustrator) {
                app.documents.add(DocumentColorSpace.RGB, 800, 600);
            }
            
            test.passed = true;
            test.message += " - تم إنشاء مستند تجريبي";
        }
    } catch (error) {
        test.message = "خطأ: " + error.message;
    }
    
    return test;
}

// اختبار وظائف الألوان الأساسية
function testColorFunctions() {
    var test = {
        name: "اختبار وظائف الألوان الأساسية",
        passed: false,
        message: ""
    };
    
    try {
        // اختبار تحويل HEX إلى RGB
        var rgb = hexToRgb("#FF5733");
        if (rgb && rgb.r === 255 && rgb.g === 87 && rgb.b === 51) {
            test.passed = true;
            test.message = "تحويل HEX إلى RGB يعمل بشكل صحيح";
        } else {
            test.message = "خطأ في تحويل HEX إلى RGB";
        }
        
        // اختبار تحويل RGB إلى HEX
        var hex = rgbToHex(255, 87, 51);
        if (hex === "#ff5733") {
            test.passed = test.passed && true;
            test.message += " | تحويل RGB إلى HEX يعمل بشكل صحيح";
        } else {
            test.passed = false;
            test.message += " | خطأ في تحويل RGB إلى HEX";
        }
        
    } catch (error) {
        test.message = "خطأ: " + error.message;
    }
    
    return test;
}

// اختبار توليد لوحات الألوان
function testPaletteGeneration() {
    var test = {
        name: "اختبار توليد لوحات الألوان",
        passed: false,
        message: ""
    };
    
    try {
        var palette = generateColorPalette("#6366f1", "complementary", 5);
        
        if (palette && palette.length === 5) {
            test.passed = true;
            test.message = "تم توليد لوحة من " + palette.length + " ألوان بنجاح";
        } else {
            test.message = "فشل في توليد لوحة الألوان";
        }
        
    } catch (error) {
        test.message = "خطأ: " + error.message;
    }
    
    return test;
}

// اختبار رسم الأشكال
function testShapeDrawing() {
    var test = {
        name: "اختبار رسم الأشكال",
        passed: false,
        message: ""
    };
    
    try {
        var testPalette = [
            {hex: "#FF5733", rgb: {r: 255, g: 87, b: 51}, name: "اختبار 1"},
            {hex: "#33FF57", rgb: {r: 51, g: 255, b: 87}, name: "اختبار 2"}
        ];
        
        if (isPhotoshop) {
            drawColorShapesPhotoshop(testPalette);
            test.passed = true;
            test.message = "تم رسم الأشكال في Photoshop بنجاح";
        } else if (isIllustrator) {
            drawColorShapesIllustrator(testPalette);
            test.passed = true;
            test.message = "تم رسم الأشكال في Illustrator بنجاح";
        } else {
            test.message = "البرنامج غير مدعوم";
        }
        
    } catch (error) {
        test.message = "خطأ: " + error.message;
    }
    
    return test;
}

// اختبار إضافة Swatches
function testSwatchesAddition() {
    var test = {
        name: "اختبار إضافة Swatches",
        passed: false,
        message: ""
    };
    
    try {
        var testPalette = [
            {hex: "#FF5733", rgb: {r: 255, g: 87, b: 51}, name: "اختبار Swatch"}
        ];
        
        if (isPhotoshop) {
            addColorsToSwatchesPhotoshop(testPalette);
            test.passed = true;
            test.message = "تم اختبار Swatches في Photoshop";
        } else if (isIllustrator) {
            addColorsToSwatchesIllustrator(testPalette);
            test.passed = true;
            test.message = "تم إضافة Swatches في Illustrator بنجاح";
        }
        
    } catch (error) {
        test.message = "خطأ: " + error.message;
    }
    
    return test;
}

// اختبار وظائف التصدير
function testExportFunctions() {
    var test = {
        name: "اختبار وظائف التصدير",
        passed: false,
        message: ""
    };
    
    try {
        var testPalette = [
            {hex: "#FF5733", rgb: {r: 255, g: 87, b: 51}, name: "اختبار تصدير"}
        ];
        
        // اختبار تحويل إلى نص
        var txtContent = generateTxtContent(testPalette);
        if (txtContent && txtContent.length > 0) {
            test.passed = true;
            test.message = "وظائف التصدير تعمل بشكل صحيح";
        } else {
            test.message = "فشل في اختبار التصدير";
        }
        
    } catch (error) {
        test.message = "خطأ: " + error.message;
    }
    
    return test;
}

// اختبار خاص بـ Photoshop
function testPhotoshopSpecific() {
    var test = {
        name: "اختبار وظائف Photoshop الخاصة",
        passed: false,
        message: ""
    };
    
    try {
        var doc = app.activeDocument;
        
        // اختبار إنشاء طبقة
        var layer = doc.artLayers.add();
        layer.name = "طبقة اختبار";
        
        test.passed = true;
        test.message = "وظائف Photoshop تعمل بشكل صحيح";
        
        // حذف طبقة الاختبار
        layer.remove();
        
    } catch (error) {
        test.message = "خطأ في Photoshop: " + error.message;
    }
    
    return test;
}

// اختبار خاص بـ Illustrator
function testIllustratorSpecific() {
    var test = {
        name: "اختبار وظائف Illustrator الخاصة",
        passed: false,
        message: ""
    };
    
    try {
        var doc = app.activeDocument;
        
        // اختبار إنشاء مستطيل
        var rect = doc.pathItems.rectangle();
        rect.top = 0;
        rect.left = 0;
        rect.width = 50;
        rect.height = 50;
        
        test.passed = true;
        test.message = "وظائف Illustrator تعمل بشكل صحيح";
        
        // حذف المستطيل
        rect.remove();
        
    } catch (error) {
        test.message = "خطأ في Illustrator: " + error.message;
    }
    
    return test;
}

// وظائف مساعدة للاختبار
function generateTxtContent(palette) {
    var content = "اختبار التصدير\n";
    for (var i = 0; i < palette.length; i++) {
        content += palette[i].name + ": " + palette[i].hex + "\n";
    }
    return content;
}

// عرض نتائج الاختبار
function displayTestResults(results) {
    var message = "نتائج اختبار التوافق\n";
    message += "========================\n";
    message += "البرنامج: " + results.appName + " " + results.appVersion + "\n";
    message += "الاختبارات الناجحة: " + results.passed + "\n";
    message += "الاختبارات الفاشلة: " + results.failed + "\n\n";
    
    message += "تفاصيل الاختبارات:\n";
    message += "-------------------\n";
    
    for (var i = 0; i < results.tests.length; i++) {
        var test = results.tests[i];
        var status = test.passed ? "✓" : "✗";
        message += status + " " + test.name + "\n";
        message += "   " + test.message + "\n\n";
    }
    
    var successRate = Math.round((results.passed / (results.passed + results.failed)) * 100);
    message += "معدل النجاح: " + successRate + "%\n";
    
    if (successRate >= 80) {
        message += "\n🎉 السكريبت متوافق بشكل ممتاز!";
    } else if (successRate >= 60) {
        message += "\n⚠️ السكريبت متوافق مع بعض المشاكل البسيطة";
    } else {
        message += "\n❌ السكريبت يحتاج لتحسينات في التوافق";
    }
    
    alert(message);
}

// تشغيل الاختبار
runCompatibilityTest();
