import React from 'react'
import { Heart, Star, Palette } from 'lucide-react'
import { ColorPalette } from '../types'

interface FavoritesViewProps {
  currentPalette: ColorPalette | null
  onPaletteChange: (palette: ColorPalette) => void
}

const FavoritesView: React.FC<FavoritesViewProps> = ({
  currentPalette,
  onPaletteChange
}) => {
  return (
    <div className="space-y-6">
      {/* عرض لوحة الألوان الحالية */}
      {currentPalette && (
        <div className="card">
          <div className="card-header">
            <h2 className="card-title">{currentPalette.name}</h2>
            <p className="card-description">
              لوحة الألوان المحددة حالياً
            </p>
          </div>
          
          {/* عرض الألوان */}
          <div className="color-grid">
            {currentPalette.colors.map((color, index) => (
              <div key={index} className="color-item">
                <div
                  className="color-preview"
                  style={{ backgroundColor: color.hex }}
                />
                <div className="color-info">
                  <div className="color-code">{color.hex.toUpperCase()}</div>
                  <div className="color-name">
                    {color.name || `لون ${index + 1}`}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* رسالة عندما لا توجد لوحة محددة */}
      {!currentPalette && (
        <div className="card text-center py-12">
          <div className="w-16 h-16 bg-primary-color/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Heart className="w-8 h-8 text-primary-color" />
          </div>
          <h3 className="text-lg font-semibold text-primary mb-2">
            لوحات الألوان المفضلة
          </h3>
          <p className="text-secondary mb-6 max-w-md mx-auto">
            اختر لوحة ألوان من الشريط الجانبي لعرضها هنا، أو قم بإنشاء لوحة جديدة من تبويب "مولد الألوان"
          </p>
          
          <div className="flex justify-center gap-4">
            <div className="text-center">
              <div className="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center mx-auto mb-2">
                <Palette className="w-6 h-6 text-muted" />
              </div>
              <p className="text-xs text-muted">مولد الألوان</p>
            </div>
            
            <div className="text-center">
              <div className="w-12 h-12 bg-secondary rounded-lg flex items-center justify-center mx-auto mb-2">
                <Star className="w-6 h-6 text-muted" />
              </div>
              <p className="text-xs text-muted">استخراج من صورة</p>
            </div>
          </div>
        </div>
      )}

      {/* نصائح وإرشادات */}
      <div className="card">
        <div className="card-header">
          <h3 className="card-title">نصائح للاستخدام</h3>
        </div>
        
        <div className="space-y-4 text-sm text-secondary">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary-color/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs font-bold text-primary-color">1</span>
            </div>
            <div>
              <strong>إنشاء لوحات جديدة:</strong> استخدم تبويب "مولد الألوان" لإنشاء لوحات متناسقة باستخدام نظريات الألوان المختلفة
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary-color/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs font-bold text-primary-color">2</span>
            </div>
            <div>
              <strong>استخراج من الصور:</strong> ارفع أي صورة في تبويب "استخراج الألوان" للحصول على الألوان المهيمنة
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary-color/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs font-bold text-primary-color">3</span>
            </div>
            <div>
              <strong>حفظ المفضلة:</strong> اضغط على زر "حفظ" في أي لوحة ألوان لإضافتها للمفضلة
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary-color/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs font-bold text-primary-color">4</span>
            </div>
            <div>
              <strong>نسخ الأكواد:</strong> انقر على أيقونة النسخ بجانب أي لون لنسخ كوده بصيغة HEX أو RGB أو HSL
            </div>
          </div>
          
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-primary-color/10 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-xs font-bold text-primary-color">5</span>
            </div>
            <div>
              <strong>التصدير:</strong> استخدم زر "تصدير" لحفظ لوحة الألوان بصيغ مختلفة مثل Adobe Swatch أو SVG أو JSON
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default FavoritesView
