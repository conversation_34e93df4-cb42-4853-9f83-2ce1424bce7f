import { ColorPalette, Color } from '../types'

// تصدير كـ JSON
export function exportAsJson(palette: ColorPalette): string {
  const exportData = {
    name: palette.name,
    colors: palette.colors.map(color => ({
      name: color.name,
      hex: color.hex,
      rgb: color.rgb,
      hsl: color.hsl
    })),
    createdAt: palette.createdAt,
    type: palette.type,
    harmony: palette.harmony
  }
  
  return JSON.stringify(exportData, null, 2)
}

// تصدير كـ SVG
export function exportAsSvg(palette: ColorPalette): string {
  const colorCount = palette.colors.length
  const swatchWidth = 100
  const swatchHeight = 100
  const totalWidth = colorCount * swatchWidth
  const totalHeight = swatchHeight + 60 // مساحة إضافية للنص

  let svg = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="${totalWidth}" height="${totalHeight}" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .color-text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .palette-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
    </style>
  </defs>
  
  <!-- عنوان اللوحة -->
  <text x="${totalWidth / 2}" y="20" class="palette-title" fill="#333">${palette.name}</text>
  
  <!-- مربعات الألوان -->`

  palette.colors.forEach((color, index) => {
    const x = index * swatchWidth
    const y = 30
    
    svg += `
  <!-- اللون ${index + 1} -->
  <rect x="${x}" y="${y}" width="${swatchWidth}" height="${swatchHeight}" fill="${color.hex}" stroke="#ccc" stroke-width="1"/>
  <text x="${x + swatchWidth / 2}" y="${y + swatchHeight + 15}" class="color-text" fill="#333">${color.hex}</text>`
    
    if (color.name) {
      svg += `
  <text x="${x + swatchWidth / 2}" y="${y + swatchHeight + 30}" class="color-text" fill="#666">${color.name}</text>`
    }
  })

  svg += `
</svg>`

  return svg
}

// تصدير كـ Adobe Swatch Exchange (.ase)
export function exportAsAse(palette: ColorPalette): Uint8Array {
  // هذا تطبيق مبسط لصيغة ASE
  // في التطبيق الحقيقي، ستحتاج لمكتبة متخصصة
  
  const colors = palette.colors
  const header = new ArrayBuffer(12)
  const headerView = new DataView(header)
  
  // ASE Header
  headerView.setUint32(0, 0x41534546, false) // "ASEF"
  headerView.setUint16(4, 1, false) // Version Major
  headerView.setUint16(6, 0, false) // Version Minor
  headerView.setUint32(8, colors.length, false) // Number of blocks
  
  const blocks: ArrayBuffer[] = []
  
  colors.forEach((color, index) => {
    // Color block
    const nameBytes = new TextEncoder().encode(color.name || `Color ${index + 1}`)
    const blockSize = 22 + nameBytes.length * 2 + 2 // تقدير تقريبي
    const block = new ArrayBuffer(blockSize)
    const blockView = new DataView(block)
    
    let offset = 0
    
    // Block type (Color)
    blockView.setUint16(offset, 0x0001, false)
    offset += 2
    
    // Block length
    blockView.setUint32(offset, blockSize - 6, false)
    offset += 4
    
    // Name length
    blockView.setUint16(offset, nameBytes.length + 1, false)
    offset += 2
    
    // Name (UTF-16)
    for (let i = 0; i < nameBytes.length; i++) {
      blockView.setUint16(offset, nameBytes[i], false)
      offset += 2
    }
    blockView.setUint16(offset, 0, false) // Null terminator
    offset += 2
    
    // Color model (RGB)
    blockView.setUint32(offset, 0x52474220, false) // "RGB "
    offset += 4
    
    // RGB values (32-bit floats)
    blockView.setFloat32(offset, color.rgb.r / 255, false)
    offset += 4
    blockView.setFloat32(offset, color.rgb.g / 255, false)
    offset += 4
    blockView.setFloat32(offset, color.rgb.b / 255, false)
    offset += 4
    
    // Color type (Global)
    blockView.setUint16(offset, 0x0002, false)
    
    blocks.push(block)
  })
  
  // دمج جميع البيانات
  const totalSize = header.byteLength + blocks.reduce((sum, block) => sum + block.byteLength, 0)
  const result = new Uint8Array(totalSize)
  
  let offset = 0
  result.set(new Uint8Array(header), offset)
  offset += header.byteLength
  
  blocks.forEach(block => {
    result.set(new Uint8Array(block), offset)
    offset += block.byteLength
  })
  
  return result
}

// تصدير كـ PNG (صورة)
export function exportAsPng(palette: ColorPalette): Promise<Blob> {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas')
    const ctx = canvas.getContext('2d')!
    
    const swatchWidth = 100
    const swatchHeight = 100
    const padding = 20
    const textHeight = 40
    
    canvas.width = palette.colors.length * swatchWidth + padding * 2
    canvas.height = swatchHeight + textHeight + padding * 2
    
    // خلفية بيضاء
    ctx.fillStyle = '#ffffff'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    // رسم الألوان
    palette.colors.forEach((color, index) => {
      const x = padding + index * swatchWidth
      const y = padding
      
      // مربع اللون
      ctx.fillStyle = color.hex
      ctx.fillRect(x, y, swatchWidth, swatchHeight)
      
      // حدود
      ctx.strokeStyle = '#cccccc'
      ctx.lineWidth = 1
      ctx.strokeRect(x, y, swatchWidth, swatchHeight)
      
      // نص اللون
      ctx.fillStyle = '#333333'
      ctx.font = '12px Arial'
      ctx.textAlign = 'center'
      ctx.fillText(color.hex, x + swatchWidth / 2, y + swatchHeight + 20)
      
      if (color.name) {
        ctx.fillStyle = '#666666'
        ctx.font = '10px Arial'
        ctx.fillText(color.name, x + swatchWidth / 2, y + swatchHeight + 35)
      }
    })
    
    // عنوان اللوحة
    ctx.fillStyle = '#333333'
    ctx.font = 'bold 16px Arial'
    ctx.textAlign = 'center'
    ctx.fillText(palette.name, canvas.width / 2, 15)
    
    canvas.toBlob((blob) => {
      resolve(blob!)
    }, 'image/png')
  })
}

// تصدير كـ TXT
export function exportAsTxt(palette: ColorPalette): string {
  let content = `${palette.name}\n`
  content += `${'='.repeat(palette.name.length)}\n\n`
  
  content += `تاريخ الإنشاء: ${new Date(palette.createdAt).toLocaleDateString('ar-SA')}\n`
  content += `نوع اللوحة: ${palette.type}\n`
  if (palette.harmony) {
    content += `نوع التناغم: ${palette.harmony}\n`
  }
  content += `عدد الألوان: ${palette.colors.length}\n\n`
  
  palette.colors.forEach((color, index) => {
    content += `${index + 1}. ${color.name || `لون ${index + 1}`}\n`
    content += `   HEX: ${color.hex}\n`
    content += `   RGB: rgb(${Math.round(color.rgb.r)}, ${Math.round(color.rgb.g)}, ${Math.round(color.rgb.b)})\n`
    content += `   HSL: hsl(${Math.round(color.hsl.h)}, ${Math.round(color.hsl.s)}%, ${Math.round(color.hsl.l)}%)\n\n`
  })
  
  content += `\n---\nتم إنشاؤه بواسطة مولد لوحات الألوان`
  
  return content
}

// تحميل ملف
export function downloadFile(content: string | Uint8Array | Blob, filename: string, mimeType: string) {
  const blob = content instanceof Blob ? content : 
               content instanceof Uint8Array ? new Blob([content], { type: mimeType }) :
               new Blob([content], { type: mimeType })
  
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)
}

// تصدير لوحة بالصيغة المحددة
export async function exportPalette(palette: ColorPalette, format: string) {
  const timestamp = new Date().toISOString().slice(0, 10)
  const baseName = `${palette.name.replace(/[^a-zA-Z0-9\u0600-\u06FF]/g, '_')}_${timestamp}`
  
  switch (format) {
    case 'json':
      const jsonContent = exportAsJson(palette)
      downloadFile(jsonContent, `${baseName}.json`, 'application/json')
      break
      
    case 'svg':
      const svgContent = exportAsSvg(palette)
      downloadFile(svgContent, `${baseName}.svg`, 'image/svg+xml')
      break
      
    case 'ase':
      const aseContent = exportAsAse(palette)
      downloadFile(aseContent, `${baseName}.ase`, 'application/octet-stream')
      break
      
    case 'png':
      const pngBlob = await exportAsPng(palette)
      downloadFile(pngBlob, `${baseName}.png`, 'image/png')
      break
      
    case 'txt':
      const txtContent = exportAsTxt(palette)
      downloadFile(txtContent, `${baseName}.txt`, 'text/plain')
      break
      
    default:
      throw new Error(`صيغة غير مدعومة: ${format}`)
  }
}
