# 🚀 دليل البدء السريع - Color Palette Generator

## ⚡ تشغيل البرنامج بسرعة

### 1. تثبيت Node.js (مرة واحدة فقط)
- حمل من: https://nodejs.org/
- ثبت النسخة LTS
- أعد تشغيل الكمبيوتر

### 2. تثبيت المكتبات (مرة واحدة فقط)
```bash
npm install
```

### 3. تشغيل البرنامج
```bash
npm run electron:dev
```

---

## 🎨 كيفية الاستخدام

### مولد الألوان:
1. اختر لوناً أساسياً
2. اختر نوع التناغم
3. اضغط "توليد لوحة"

### استخراج من صورة:
1. اذهب لتبويب "استخراج الألوان"
2. اسحب صورة أو انقر لاختيارها
3. البرنامج سيستخرج الألوان تلقائياً

### حفظ وتصدير:
- اضغط "حفظ" لإضافة للمفضلة
- اضغط "تصدير" لحفظ بصيغ مختلفة

---

## 🔧 حل المشاكل السريع

**مشكلة:** `npm is not recognized`
**الحل:** ثبت Node.js وأعد تشغيل الكمبيوتر

**مشكلة:** البرنامج لا يفتح
**الحل:** تأكد من تشغيل `npm install` أولاً

---

## 📁 الملفات المهمة

- `README.md` - دليل شامل
- `SETUP_GUIDE.md` - دليل التثبيت المفصل
- `package.json` - إعدادات المشروع

---

**🎯 هدف البرنامج:** مساعدة المصممين في إنشاء لوحات ألوان احترافية بسهولة!
