import { Color, ColorHarmony } from '../types'

// تحويل HEX إلى RGB
export function hexToRgb(hex: string): { r: number; g: number; b: number } {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 }
}

// تحويل RGB إلى HEX
export function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (Math.round(r) << 16) + (Math.round(g) << 8) + Math.round(b)).toString(16).slice(1)
}

// تحويل RGB إلى HSL
export function rgbToHsl(r: number, g: number, b: number): { h: number; s: number; l: number } {
  r /= 255
  g /= 255
  b /= 255

  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  let h = 0
  let s = 0
  const l = (max + min) / 2

  if (max === min) {
    h = s = 0 // achromatic
  } else {
    const d = max - min
    s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

    switch (max) {
      case r: h = (g - b) / d + (g < b ? 6 : 0); break
      case g: h = (b - r) / d + 2; break
      case b: h = (r - g) / d + 4; break
    }
    h /= 6
  }

  return {
    h: h * 360,
    s: s * 100,
    l: l * 100
  }
}

// تحويل HSL إلى RGB
export function hslToRgb(h: number, s: number, l: number): { r: number; g: number; b: number } {
  h /= 360
  s /= 100
  l /= 100

  const hue2rgb = (p: number, q: number, t: number) => {
    if (t < 0) t += 1
    if (t > 1) t -= 1
    if (t < 1/6) return p + (q - p) * 6 * t
    if (t < 1/2) return q
    if (t < 2/3) return p + (q - p) * (2/3 - t) * 6
    return p
  }

  let r, g, b

  if (s === 0) {
    r = g = b = l // achromatic
  } else {
    const q = l < 0.5 ? l * (1 + s) : l + s - l * s
    const p = 2 * l - q
    r = hue2rgb(p, q, h + 1/3)
    g = hue2rgb(p, q, h)
    b = hue2rgb(p, q, h - 1/3)
  }

  return {
    r: r * 255,
    g: g * 255,
    b: b * 255
  }
}

// إنشاء كائن لون كامل
export function createColor(hex: string, name?: string): Color {
  const rgb = hexToRgb(hex)
  const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
  
  return {
    id: `color_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    hex: hex.toUpperCase(),
    rgb,
    hsl,
    name
  }
}

// توليد لون عشوائي
export function generateRandomColor(): string {
  return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')
}

// توليد لوحة ألوان بناءً على نظرية الألوان
export function generateColorPalette(baseColor: string, harmony: ColorHarmony, count: number): Color[] {
  const baseRgb = hexToRgb(baseColor)
  const baseHsl = rgbToHsl(baseRgb.r, baseRgb.g, baseRgb.b)
  const colors: Color[] = []

  // إضافة اللون الأساسي
  colors.push(createColor(baseColor, 'اللون الأساسي'))

  switch (harmony) {
    case 'monochromatic':
      // تدرجات من نفس اللون
      for (let i = 1; i < count; i++) {
        const lightness = Math.max(10, Math.min(90, baseHsl.l + (i - count/2) * 15))
        const rgb = hslToRgb(baseHsl.h, baseHsl.s, lightness)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `تدرج ${i}`))
      }
      break

    case 'analogous':
      // ألوان متجاورة
      for (let i = 1; i < count; i++) {
        const hue = (baseHsl.h + (i * 30)) % 360
        const rgb = hslToRgb(hue, baseHsl.s, baseHsl.l)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `متجاور ${i}`))
      }
      break

    case 'complementary':
      // ألوان مكملة
      const complementaryHue = (baseHsl.h + 180) % 360
      const complementaryRgb = hslToRgb(complementaryHue, baseHsl.s, baseHsl.l)
      colors.push(createColor(rgbToHex(complementaryRgb.r, complementaryRgb.g, complementaryRgb.b), 'مكمل'))
      
      // إضافة تدرجات
      for (let i = 2; i < count; i++) {
        const isBase = i % 2 === 0
        const hue = isBase ? baseHsl.h : complementaryHue
        const lightness = Math.max(10, Math.min(90, baseHsl.l + (i - count/2) * 10))
        const rgb = hslToRgb(hue, baseHsl.s * 0.8, lightness)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `تدرج ${i}`))
      }
      break

    case 'triadic':
      // ثلاثة ألوان متباعدة
      for (let i = 1; i < Math.min(3, count); i++) {
        const hue = (baseHsl.h + (i * 120)) % 360
        const rgb = hslToRgb(hue, baseHsl.s, baseHsl.l)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `ثلاثي ${i}`))
      }
      
      // إضافة تدرجات إضافية
      for (let i = 3; i < count; i++) {
        const baseIndex = i % 3
        const baseColorHsl = rgbToHsl(...Object.values(colors[baseIndex].rgb))
        const lightness = Math.max(10, Math.min(90, baseColorHsl.l + (Math.random() - 0.5) * 30))
        const rgb = hslToRgb(baseColorHsl.h, baseColorHsl.s * 0.8, lightness)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `تدرج ${i}`))
      }
      break

    case 'tetradic':
      // أربعة ألوان تشكل مستطيل
      const hues = [baseHsl.h, (baseHsl.h + 90) % 360, (baseHsl.h + 180) % 360, (baseHsl.h + 270) % 360]
      for (let i = 1; i < Math.min(4, count); i++) {
        const rgb = hslToRgb(hues[i], baseHsl.s, baseHsl.l)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `رباعي ${i}`))
      }
      
      // إضافة تدرجات إضافية
      for (let i = 4; i < count; i++) {
        const baseIndex = i % 4
        const hue = hues[baseIndex]
        const lightness = Math.max(10, Math.min(90, baseHsl.l + (Math.random() - 0.5) * 25))
        const rgb = hslToRgb(hue, baseHsl.s * 0.9, lightness)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `تدرج ${i}`))
      }
      break

    case 'splitComplementary':
      // مكمل منقسم
      const splitHue1 = (baseHsl.h + 150) % 360
      const splitHue2 = (baseHsl.h + 210) % 360
      
      const split1Rgb = hslToRgb(splitHue1, baseHsl.s, baseHsl.l)
      const split2Rgb = hslToRgb(splitHue2, baseHsl.s, baseHsl.l)
      
      colors.push(createColor(rgbToHex(split1Rgb.r, split1Rgb.g, split1Rgb.b), 'مكمل منقسم 1'))
      colors.push(createColor(rgbToHex(split2Rgb.r, split2Rgb.g, split2Rgb.b), 'مكمل منقسم 2'))
      
      // إضافة تدرجات
      for (let i = 3; i < count; i++) {
        const hues = [baseHsl.h, splitHue1, splitHue2]
        const hue = hues[i % 3]
        const lightness = Math.max(10, Math.min(90, baseHsl.l + (Math.random() - 0.5) * 20))
        const rgb = hslToRgb(hue, baseHsl.s * 0.85, lightness)
        const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
        colors.push(createColor(hex, `تدرج ${i}`))
      }
      break
  }

  return colors.slice(0, count)
}

// استخراج الألوان من صورة
export async function extractColorsFromImage(imageUrl: string, colorCount: number = 6): Promise<Color[]> {
  return new Promise((resolve, reject) => {
    const img = new Image()
    img.crossOrigin = 'anonymous'
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        
        if (!ctx) {
          reject(new Error('فشل في إنشاء canvas context'))
          return
        }

        // تصغير الصورة لتحسين الأداء
        const maxSize = 150
        const ratio = Math.min(maxSize / img.width, maxSize / img.height)
        canvas.width = img.width * ratio
        canvas.height = img.height * ratio

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height)
        const pixels = imageData.data
        
        // تجميع الألوان
        const colorMap = new Map<string, number>()
        
        for (let i = 0; i < pixels.length; i += 4) {
          const r = pixels[i]
          const g = pixels[i + 1]
          const b = pixels[i + 2]
          const a = pixels[i + 3]
          
          // تجاهل الألوان الشفافة والألوان القريبة من الأبيض والأسود
          if (a < 128) continue
          if (r > 240 && g > 240 && b > 240) continue
          if (r < 15 && g < 15 && b < 15) continue
          
          // تقريب الألوان لتقليل التنوع
          const roundedR = Math.round(r / 10) * 10
          const roundedG = Math.round(g / 10) * 10
          const roundedB = Math.round(b / 10) * 10
          
          const colorKey = `${roundedR},${roundedG},${roundedB}`
          colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1)
        }
        
        // ترتيب الألوان حسب التكرار
        const sortedColors = Array.from(colorMap.entries())
          .sort((a, b) => b[1] - a[1])
          .slice(0, colorCount)
        
        // تحويل إلى كائنات Color
        const colors: Color[] = sortedColors.map((entry, index) => {
          const [colorKey] = entry
          const [r, g, b] = colorKey.split(',').map(Number)
          const hex = rgbToHex(r, g, b)
          return createColor(hex, `لون مهيمن ${index + 1}`)
        })
        
        resolve(colors)
      } catch (error) {
        reject(error)
      }
    }
    
    img.onerror = () => {
      reject(new Error('فشل في تحميل الصورة'))
    }
    
    img.src = imageUrl
  })
}
