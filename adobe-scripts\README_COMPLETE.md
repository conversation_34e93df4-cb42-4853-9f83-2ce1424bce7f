# 🎨 Color Palette Generator for Adobe Scripts - مولد لوحات الألوان

مجموعة شاملة ومتقدمة من السكريبتات لتوليد لوحات الألوان في Adobe Photoshop و Illustrator

## 🚀 النسخ المتاحة

### 🎯 **النسخة المتقدمة الجديدة** ⭐
- **`ColorPaletteGenerator_Advanced.jsx`** - النسخة الشاملة مع جميع المميزات

### 🎨 **النسخ المتخصصة:**
- **`ColorPaletteGenerator_Clean.jsx`** - نسخة نظيفة (مربعات ألوان فقط)
- **`ColorPaletteGenerator_Fixed.jsx`** - نسخة محسنة (مع النصوص)
- **`ColorPaletteGenerator.jsx`** - النسخة الأصلية

### 🔧 **الأدوات المساعدة:**
- `ColorHarmonyEngine.jsx` - محرك التناغم اللوني المتقدم  
- `ImageColorExtractor.jsx` - مستخرج الألوان من الصور
- `ColorUtils.jsx` - وظائف مساعدة للألوان

---

## ✨ المميزات الجديدة في النسخة المتقدمة

### 🎨 **أشكال متنوعة (8 أشكال):**
- ■ مربع - ● دائرة - ▲ مثلث - ⬡ سداسي
- ◆ معين - ★ نجمة - ♥ قلب - 💧 قطرة

### 🎭 **تأثيرات بصرية (7 تأثيرات):**
- بدون تأثير - ظل - تدرج - معدني
- توهج - نقش بارز - زجاجي

### 📐 **تخطيطات ترتيب (6 تخطيطات):**
- أفقي - عمودي - شبكة
- دائري - موجي - حلزوني

### 🌍 **ألوان تراثية عربية:**
- **تراثي عربي**: أحمر تراثي، ذهبي عربي، أخضر إسلامي
- **رمضان**: أزرق ليلي، ذهبي هلال، أخضر مسجد
- **عيد**: أخضر عيد، ذهبي احتفال، أحمر فرح

### 📊 **تحليل متقدم:**
- تحليل حرارة الألوان (دافئ/بارد/محايد)
- حساب التباين بين الألوان
- تقييم إمكانية الوصول (WCAG)
- توصيات ذكية لتحسين اللوحة

### 💾 **تصدير متعدد الصيغ:**
- **CSS** - متغيرات وفئات جاهزة
- **SCSS** - متغيرات ودوال SASS
- **JSON** - بيانات شاملة مع التحليل
- **HTML** - صفحة ويب تفاعلية
- **TXT** - ملف نصي منظم

---

## 📋 التثبيت والاستخدام

### 🔧 **التثبيت:**
1. **حمل الملفات** من هذا المجلد
2. **انسخها إلى مجلد Scripts**:
   - **Windows**: `C:\Program Files\Adobe\[Program]\Presets\Scripts\`
   - **macOS**: `/Applications/Adobe [Program]/Presets/Scripts/`
3. **أعد تشغيل البرنامج**
4. **ستجد السكريبتات** في `File > Scripts`

### 🚀 **الاستخدام السريع:**

#### **للمبتدئين - النسخة النظيفة:**
1. شغل `ColorPaletteGenerator_Clean.jsx`
2. أدخل كود اللون (مثل: #6366f1)
3. اختر نوع التناغم
4. اضغط "توليد لوحة الألوان"

#### **للمتقدمين - النسخة الشاملة:**
1. شغل `ColorPaletteGenerator_Advanced.jsx`
2. **التبويب الأول**: اختر اللون ونوع التناغم
3. **التبويب الثاني**: حدد الشكل والتأثيرات والتخطيط
4. **التبويب الثالث**: فعل التحليل وخيارات التصدير
5. اضغط "توليد لوحة الألوان"

---

## 🎨 أنواع التناغم المدعومة

### 🎯 **الأنواع التقليدية:**
1. **مكمل** - ألوان متقابلة في عجلة الألوان
2. **متجاور** - ألوان متجاورة ومتناسقة
3. **ثلاثي** - ثلاثة ألوان متباعدة بالتساوي
4. **رباعي** - أربعة ألوان متوازنة
5. **أحادي اللون** - تدرجات من نفس اللون
6. **مكمل منقسم** - تناغم متقدم ومتطور

### 🌍 **الأنواع الثقافية الجديدة:**
7. **تراثي عربي** - ألوان من التراث العربي الأصيل
8. **رمضان** - ألوان مستوحاة من الشهر الكريم
9. **عيد** - ألوان احتفالية مفرحة

---

## 📊 مقارنة النسخ

| الميزة | النسخة الأصلية | النسخة النظيفة | النسخة المحسنة | النسخة المتقدمة |
|--------|----------------|-----------------|-----------------|------------------|
| مربعات الألوان | ✅ | ✅ | ✅ | ✅ |
| نصوص الألوان | ❌ | ❌ | ✅ | اختياري |
| أشكال متنوعة | ❌ | ❌ | ❌ | ✅ (8 أشكال) |
| تأثيرات بصرية | ❌ | ❌ | ❌ | ✅ (7 تأثيرات) |
| تخطيطات ترتيب | ❌ | ❌ | ❌ | ✅ (6 تخطيطات) |
| ألوان تراثية | ❌ | ❌ | ❌ | ✅ (3 مجموعات) |
| تحليل الألوان | ❌ | ❌ | ❌ | ✅ شامل |
| تصدير متقدم | ❌ | ❌ | ❌ | ✅ (5 صيغ) |
| واجهة متقدمة | ❌ | ❌ | ❌ | ✅ (3 تبويبات) |

---

## 🎯 أي نسخة تختار؟

### 🎨 **النسخة النظيفة** - للبساطة والأناقة
**استخدمها عندما:**
- تريد مظهر نظيف وبسيط
- تعمل على عروض تقديمية
- تحتاج سرعة في التنفيذ
- تريد طباعة لوحات الألوان

### 🔧 **النسخة المحسنة** - للمعلومات التفصيلية
**استخدمها عندما:**
- تحتاج أكواد الألوان ظاهرة
- تعمل مع فريق تطوير
- تحتاج توثيق كامل للألوان
- تعمل على مشاريع تقنية

### 🚀 **النسخة المتقدمة** - للاحترافية الكاملة ⭐
**استخدمها عندما:**
- تريد جميع المميزات المتقدمة
- تحتاج أشكال وتأثيرات متنوعة
- تعمل على مشاريع ثقافية عربية
- تحتاج تحليل شامل للألوان
- تريد تصدير بصيغ متعددة

---

## 📚 الأدلة والتوثيق

### 📖 **الأدلة المتاحة:**
- **`ADVANCED_FEATURES_GUIDE.md`** - دليل المميزات المتقدمة الشامل
- **`CLEAN_VERSION_GUIDE.md`** - دليل النسخة النظيفة
- **`FIXED_VERSION_GUIDE.md`** - دليل النسخة المحسنة
- **`LAYOUT_IMPROVEMENTS.md`** - دليل تحسينات التخطيط

### 🔧 **للمطورين:**
- **`DEVELOPER_GUIDE.md`** - دليل المطورين
- **`API_REFERENCE.md`** - مرجع الوظائف
- **`CUSTOMIZATION_GUIDE.md`** - دليل التخصيص

---

## 🎉 أمثلة الاستخدام

### 🌐 **للمواقع الإلكترونية:**
```
اللون الأساسي: #3498db
النوع: مكمل
الشكل: دائرة
التأثير: توهج
النتيجة: لوحة ألوان حديثة للويب
```

### 🏢 **للهوية البصرية:**
```
اللون الأساسي: #e74c3c  
النوع: متجاور
الشكل: مربع
التأثير: ظل
النتيجة: ألوان متناسقة للعلامة التجارية
```

### 🕌 **للمشاريع العربية:**
```
النوع: تراثي عربي
الشكل: نجمة
التخطيط: دائري
النتيجة: لوحة ألوان أصيلة وجذابة
```

### 🎨 **للمشاريع الفنية:**
```
اللون الأساسي: #9b59b6
النوع: ثلاثي
الشكل: قطرة
التخطيط: حلزوني
التأثير: معدني
النتيجة: تصميم فني مبتكر
```

---

## 🔧 المتطلبات التقنية

### 💻 **البرامج المدعومة:**
- Adobe Photoshop CS6 أو أحدث
- Adobe Illustrator CS6 أو أحدث

### 🖥️ **أنظمة التشغيل:**
- Windows 7+ 
- macOS 10.12+

### ⚡ **الأداء:**
- **النسخة النظيفة**: سريعة جداً
- **النسخة المحسنة**: سريعة
- **النسخة المتقدمة**: متوسطة (بسبب المميزات الإضافية)

---

## 🆘 المشاكل الشائعة والحلول

### ❌ **"كود اللون غير صحيح"**
**الحل**: استخدم تنسيق HEX صحيح:
- ✅ `#FF5733` (6 أحرف)
- ✅ `#F53` (3 أحرف)
- ❌ `FF5733` (بدون #)

### ❌ **"لا يوجد مستند مفتوح"**
**الحل**: 
- افتح مستند جديد أو موجود
- أو اختر "نعم" عندما يسأل السكريبت

### ❌ **"فشل في رسم الألوان"**
**الحل**:
- تأكد من وجود مساحة كافية
- قلل عدد الألوان المطلوبة
- أعد تشغيل البرنامج

---

## 🔄 التحديثات والإصدارات

### 📅 **الإصدار الحالي: 2.0.0 - Advanced**
- ✅ 8 أشكال جديدة للألوان
- ✅ 7 تأثيرات بصرية متقدمة
- ✅ 6 تخطيطات ترتيب مبتكرة
- ✅ ألوان تراثية عربية أصيلة
- ✅ تحليل شامل للألوان والتباين
- ✅ تصدير متعدد الصيغ
- ✅ واجهة متقدمة بعلامات تبويب

### 🔮 **قريباً في الإصدارات القادمة:**
- 🎯 أشكال ثلاثية الأبعاد
- 🎭 تأثيرات متحركة
- 🤖 ذكاء اصطناعي لاقتراح الألوان
- 🔗 تكامل مع Figma
- 📱 دعم تطبيقات الهاتف المحمول

---

**🎨 استمتع بإنشاء لوحات ألوان رائعة ومتقدمة! 🎨**

*تم تطوير هذه الأدوات بعناية لتوفير أفضل تجربة ممكنة للمصممين والمطورين العرب* 🌟
