import React from 'react'
import { AppTab, ColorPalette } from '../types'
import ColorGenerator from './ColorGenerator'
import ColorExtractor from './ColorExtractor'
import FavoritesView from './FavoritesView'

interface MainContentProps {
  activeTab: AppTab
  currentPalette: ColorPalette | null
  onPaletteChange: (palette: ColorPalette) => void
  onAddToFavorites: (palette: ColorPalette) => void
}

const MainContent: React.FC<MainContentProps> = ({
  activeTab,
  currentPalette,
  onPaletteChange,
  onAddToFavorites
}) => {
  const renderContent = () => {
    switch (activeTab) {
      case 'generator':
        return (
          <ColorGenerator
            currentPalette={currentPalette}
            onPaletteChange={onPaletteChange}
            onAddToFavorites={onAddToFavorites}
          />
        )
      
      case 'extractor':
        return (
          <ColorExtractor
            currentPalette={currentPalette}
            onPaletteChange={onPaletteChange}
            onAddToFavorites={onAddToFavorites}
          />
        )
      
      case 'favorites':
        return (
          <FavoritesView
            currentPalette={currentPalette}
            onPaletteChange={onPaletteChange}
          />
        )
      
      default:
        return null
    }
  }

  return (
    <div className="main-body">
      {renderContent()}
    </div>
  )
}

export default MainContent
