import React, { useState } from 'react'
import { X, Download, FileText, Image, Code, Palette as PaletteIcon } from 'lucide-react'
import { ColorPalette } from '../types'
import { exportPalette } from '../utils/exportUtils'

interface ExportModalProps {
  palette: ColorPalette
  isOpen: boolean
  onClose: () => void
}

const ExportModal: React.FC<ExportModalProps> = ({ palette, isOpen, onClose }) => {
  const [isExporting, setIsExporting] = useState(false)
  const [selectedFormat, setSelectedFormat] = useState<string>('json')

  const exportFormats = [
    {
      id: 'json',
      name: 'JSON',
      description: 'ملف JSON للمطورين والتطبيقات',
      icon: Code,
      extension: '.json'
    },
    {
      id: 'svg',
      name: 'SVG',
      description: 'صورة متجهة قابلة للتحرير',
      icon: Image,
      extension: '.svg'
    },
    {
      id: 'ase',
      name: 'Adobe Swatch',
      description: 'ملف ASE لبرامج Adobe (Photoshop, Illustrator)',
      icon: PaletteIcon,
      extension: '.ase'
    },
    {
      id: 'png',
      name: 'PNG Image',
      description: 'صورة للعرض والمشاركة',
      icon: Image,
      extension: '.png'
    },
    {
      id: 'txt',
      name: 'Text File',
      description: 'ملف نصي بسيط مع جميع المعلومات',
      icon: FileText,
      extension: '.txt'
    }
  ]

  const handleExport = async () => {
    if (!palette) return

    setIsExporting(true)
    try {
      await exportPalette(palette, selectedFormat)
      // يمكن إضافة رسالة نجاح هنا
    } catch (error) {
      console.error('خطأ في التصدير:', error)
      alert('حدث خطأ أثناء التصدير')
    } finally {
      setIsExporting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-primary rounded-lg shadow-lg max-w-md w-full mx-4">
        {/* رأس النافذة */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-lg font-semibold text-primary">تصدير لوحة الألوان</h2>
          <button
            onClick={onClose}
            className="text-muted hover:text-primary transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* محتوى النافذة */}
        <div className="p-6">
          {/* معلومات اللوحة */}
          <div className="mb-6">
            <h3 className="font-medium text-primary mb-2">{palette.name}</h3>
            <p className="text-sm text-secondary">
              {palette.colors.length} لون • {palette.type}
            </p>
          </div>

          {/* اختيار صيغة التصدير */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-primary mb-3">
              اختر صيغة التصدير:
            </label>
            <div className="space-y-2">
              {exportFormats.map((format) => {
                const Icon = format.icon
                return (
                  <label
                    key={format.id}
                    className={`flex items-start gap-3 p-3 rounded-lg border cursor-pointer transition-all ${
                      selectedFormat === format.id
                        ? 'border-primary-color bg-primary-color/5'
                        : 'border-border hover:border-primary-color/50'
                    }`}
                  >
                    <input
                      type="radio"
                      name="exportFormat"
                      value={format.id}
                      checked={selectedFormat === format.id}
                      onChange={(e) => setSelectedFormat(e.target.value)}
                      className="mt-1"
                    />
                    <Icon className="w-5 h-5 text-primary-color flex-shrink-0 mt-0.5" />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium text-primary">{format.name}</span>
                        <span className="text-xs text-muted font-mono">
                          {format.extension}
                        </span>
                      </div>
                      <p className="text-sm text-secondary mt-1">
                        {format.description}
                      </p>
                    </div>
                  </label>
                )
              })}
            </div>
          </div>

          {/* معاينة الألوان */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-primary mb-2">
              معاينة الألوان:
            </label>
            <div className="flex gap-1 p-3 bg-secondary rounded-lg">
              {palette.colors.map((color, index) => (
                <div
                  key={index}
                  className="w-8 h-8 rounded border border-border flex-shrink-0"
                  style={{ backgroundColor: color.hex }}
                  title={`${color.name || `لون ${index + 1}`} - ${color.hex}`}
                />
              ))}
            </div>
          </div>
        </div>

        {/* أزرار التحكم */}
        <div className="flex gap-3 p-6 border-t border-border">
          <button
            onClick={onClose}
            className="btn btn-secondary flex-1"
          >
            إلغاء
          </button>
          <button
            onClick={handleExport}
            disabled={isExporting}
            className="btn btn-primary flex-1"
          >
            {isExporting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                جاري التصدير...
              </>
            ) : (
              <>
                <Download className="w-4 h-4" />
                تصدير
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ExportModal
