import React, { useState, useCallback } from 'react'
import { Shuffle, Heart, Download, Copy, Check } from 'lucide-react'
import { ColorPalette, ColorHarmony, Color } from '../types'
import { generateColorPalette, generateRandomColor } from '../utils/colorUtils'
import ColorDisplay from './ColorDisplay'
import ExportModal from './ExportModal'

interface ColorGeneratorProps {
  currentPalette: ColorPalette | null
  onPaletteChange: (palette: ColorPalette) => void
  onAddToFavorites: (palette: ColorPalette) => void
}

const ColorGenerator: React.FC<ColorGeneratorProps> = ({
  currentPalette,
  onPaletteChange,
  onAddToFavorites
}) => {
  const [baseColor, setBaseColor] = useState('#6366f1')
  const [harmony, setHarmony] = useState<ColorHarmony>('complementary')
  const [colorCount, setColorCount] = useState(5)
  const [isGenerating, setIsGenerating] = useState(false)
  const [showExportModal, setShowExportModal] = useState(false)

  const harmonies: { value: ColorHarmony; label: string; description: string }[] = [
    { value: 'monochromatic', label: 'أحادي اللون', description: 'تدرجات من نفس اللون' },
    { value: 'analogous', label: 'متجاور', description: 'ألوان متجاورة في عجلة الألوان' },
    { value: 'complementary', label: 'مكمل', description: 'ألوان متقابلة في عجلة الألوان' },
    { value: 'triadic', label: 'ثلاثي', description: 'ثلاثة ألوان متباعدة بالتساوي' },
    { value: 'tetradic', label: 'رباعي', description: 'أربعة ألوان تشكل مستطيل' },
    { value: 'splitComplementary', label: 'مكمل منقسم', description: 'لون أساسي مع لونين مجاورين لمكمله' }
  ]

  const handleGeneratePalette = useCallback(async () => {
    setIsGenerating(true)
    
    try {
      // محاكاة تأخير للتأثير البصري
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const colors = generateColorPalette(baseColor, harmony, colorCount)
      
      const newPalette: ColorPalette = {
        id: `palette_${Date.now()}`,
        name: `لوحة ${harmonies.find(h => h.value === harmony)?.label} - ${new Date().toLocaleDateString('ar-SA')}`,
        colors,
        createdAt: new Date(),
        type: 'generated',
        harmony
      }
      
      onPaletteChange(newPalette)
    } catch (error) {
      console.error('خطأ في توليد لوحة الألوان:', error)
    } finally {
      setIsGenerating(false)
    }
  }, [baseColor, harmony, colorCount, onPaletteChange])

  const handleRandomColor = () => {
    const randomColor = generateRandomColor()
    setBaseColor(randomColor)
  }

  const handleAddToFavorites = () => {
    if (currentPalette) {
      onAddToFavorites(currentPalette)
    }
  }

  return (
    <div className="space-y-6">
      {/* إعدادات التوليد */}
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">إعدادات مولد الألوان</h2>
          <p className="card-description">
            اختر اللون الأساسي ونوع التناغم لتوليد لوحة ألوان متناسقة
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* اللون الأساسي */}
          <div>
            <label className="block text-sm font-medium text-primary mb-2">
              اللون الأساسي
            </label>
            <div className="flex gap-2">
              <input
                type="color"
                value={baseColor}
                onChange={(e) => setBaseColor(e.target.value)}
                className="w-12 h-10 rounded border border-border cursor-pointer"
              />
              <input
                type="text"
                value={baseColor}
                onChange={(e) => setBaseColor(e.target.value)}
                className="flex-1 px-3 py-2 border border-border rounded-md text-sm font-mono"
                placeholder="#6366f1"
              />
              <button
                onClick={handleRandomColor}
                className="btn btn-secondary btn-icon"
                title="لون عشوائي"
              >
                <Shuffle className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* نوع التناغم */}
          <div>
            <label className="block text-sm font-medium text-primary mb-2">
              نوع التناغم
            </label>
            <select
              value={harmony}
              onChange={(e) => setHarmony(e.target.value as ColorHarmony)}
              className="w-full px-3 py-2 border border-border rounded-md text-sm"
            >
              {harmonies.map((h) => (
                <option key={h.value} value={h.value}>
                  {h.label}
                </option>
              ))}
            </select>
          </div>

          {/* عدد الألوان */}
          <div>
            <label className="block text-sm font-medium text-primary mb-2">
              عدد الألوان ({colorCount})
            </label>
            <input
              type="range"
              min="3"
              max="10"
              value={colorCount}
              onChange={(e) => setColorCount(parseInt(e.target.value))}
              className="w-full"
            />
          </div>

          {/* زر التوليد */}
          <div className="flex items-end">
            <button
              onClick={handleGeneratePalette}
              disabled={isGenerating}
              className="btn btn-primary w-full"
            >
              {isGenerating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  جاري التوليد...
                </>
              ) : (
                <>
                  <Shuffle className="w-4 h-4" />
                  توليد لوحة
                </>
              )}
            </button>
          </div>
        </div>

        {/* وصف التناغم المحدد */}
        <div className="mt-4 p-3 bg-secondary rounded-lg">
          <p className="text-sm text-secondary">
            <strong>{harmonies.find(h => h.value === harmony)?.label}:</strong>{' '}
            {harmonies.find(h => h.value === harmony)?.description}
          </p>
        </div>
      </div>

      {/* عرض لوحة الألوان */}
      {currentPalette && (
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="card-title">{currentPalette.name}</h2>
                <p className="card-description">
                  {currentPalette.colors.length} لون • {currentPalette.harmony}
                </p>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={handleAddToFavorites}
                  className="btn btn-secondary"
                  title="إضافة للمفضلة"
                >
                  <Heart className="w-4 h-4" />
                  حفظ
                </button>
                
                <button
                  onClick={() => setShowExportModal(true)}
                  className="btn btn-secondary"
                  title="تصدير"
                >
                  <Download className="w-4 h-4" />
                  تصدير
                </button>
              </div>
            </div>
          </div>

          <ColorDisplay
            colors={currentPalette.colors}
            showCopyButtons={true}
          />
        </div>
      )}

      {/* رسالة ترحيبية */}
      {!currentPalette && (
        <div className="card text-center py-12">
          <div className="w-16 h-16 bg-primary-color/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Shuffle className="w-8 h-8 text-primary-color" />
          </div>
          <h3 className="text-lg font-semibold text-primary mb-2">
            مرحباً بك في مولد لوحات الألوان
          </h3>
          <p className="text-secondary mb-6 max-w-md mx-auto">
            اختر لوناً أساسياً ونوع التناغم، ثم اضغط على "توليد لوحة" للحصول على لوحة ألوان متناسقة
          </p>
          <button
            onClick={handleGeneratePalette}
            className="btn btn-primary"
          >
            <Shuffle className="w-4 h-4" />
            ابدأ بتوليد لوحة
          </button>
        </div>
      )}

      {/* نافذة التصدير */}
      {currentPalette && (
        <ExportModal
          palette={currentPalette}
          isOpen={showExportModal}
          onClose={() => setShowExportModal(false)}
        />
      )}
    </div>
  )
}

export default ColorGenerator
