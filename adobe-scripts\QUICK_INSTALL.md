# 🚀 دليل التثبيت السريع - Color Palette Generator

## ⚡ التثبيت في 3 خطوات

### الخطوة 1: تحميل الملفات
حمل هذه الملفات:
- `ColorPaletteGenerator.jsx` (الملف الرئيسي)
- `ColorHarmonyEngine.jsx` (اختياري - للوظائف المتقدمة)
- `ImageColorExtractor.jsx` (اختياري - لاستخراج الألوان)

### الخطوة 2: نسخ الملفات
انسخ الملفات إلى مجلد Scripts:

**Windows:**
```
C:\Program Files\Adobe\Adobe Photoshop [Version]\Presets\Scripts\
C:\Program Files\Adobe\Adobe Illustrator [Version]\Presets\Scripts\
```

**macOS:**
```
/Applications/Adobe Photoshop [Version]/Presets/Scripts/
/Applications/Adobe Illustrator [Version]/Presets/Scripts/
```

### الخطوة 3: تشغيل السكريبت
1. افتح Photoshop أو Illustrator
2. اذهب إلى `File > Scripts > ColorPaletteGenerator`
3. أو `File > Scripts > Browse...` واختر الملف

---

## 🎯 الاستخدام السريع

### توليد لوحة ألوان:
1. أدخل كود اللون: `#6366f1`
2. اختر نوع التناغم: "مكمل"
3. حدد عدد الألوان: `5`
4. اضغط "توليد لوحة الألوان"

### استخراج من صورة (Photoshop):
1. افتح صورة
2. حدد الطبقة
3. اختر "استخراج من صورة محددة"
4. اضغط "توليد لوحة الألوان"

---

## 🔧 حل المشاكل السريع

**مشكلة:** السكريبت لا يظهر في القائمة
**الحل:** أعد تشغيل البرنامج بعد نسخ الملفات

**مشكلة:** "لا يوجد مستند مفتوح"
**الحل:** افتح مستند جديد أولاً

**مشكلة:** خطأ في تشغيل السكريبت
**الحل:** تأكد من استخدام Photoshop CS6+ أو Illustrator CC+

---

## 📞 مساعدة سريعة

- للدليل الكامل: راجع `README.md`
- لاختبار التوافق: شغل `CompatibilityTest.jsx`
- للمشاكل المتقدمة: راجع قسم "استكشاف الأخطاء" في الدليل الكامل

**🎨 استمتع بإنشاء لوحات ألوان رائعة!**
