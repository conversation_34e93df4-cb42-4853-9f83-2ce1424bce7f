import React, { useState, useRef, useCallback } from 'react'
import { Upload, Image as ImageIcon, Heart, Download, Loader } from 'lucide-react'
import { ColorPalette, Color } from '../types'
import { extractColorsFromImage } from '../utils/colorUtils'
import ColorDisplay from './ColorDisplay'
import ExportModal from './ExportModal'

interface ColorExtractorProps {
  currentPalette: ColorPalette | null
  onPaletteChange: (palette: ColorPalette) => void
  onAddToFavorites: (palette: ColorPalette) => void
}

const ColorExtractor: React.FC<ColorExtractorProps> = ({
  currentPalette,
  onPaletteChange,
  onAddToFavorites
}) => {
  const [selectedImage, setSelectedImage] = useState<string | null>(null)
  const [isExtracting, setIsExtracting] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [colorCount, setColorCount] = useState(6)
  const [showExportModal, setShowExportModal] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = useCallback(async (file: File) => {
    if (!file.type.startsWith('image/')) {
      alert('يرجى اختيار ملف صورة صالح')
      return
    }

    const reader = new FileReader()
    reader.onload = async (e) => {
      const imageUrl = e.target?.result as string
      setSelectedImage(imageUrl)
      await extractColors(imageUrl, file.name)
    }
    reader.readAsDataURL(file)
  }, [colorCount])

  const extractColors = async (imageUrl: string, fileName: string) => {
    setIsExtracting(true)
    
    try {
      const colors = await extractColorsFromImage(imageUrl, colorCount)
      
      const newPalette: ColorPalette = {
        id: `palette_${Date.now()}`,
        name: `ألوان من ${fileName} - ${new Date().toLocaleDateString('ar-SA')}`,
        colors,
        createdAt: new Date(),
        type: 'extracted',
        sourceImage: imageUrl
      }
      
      onPaletteChange(newPalette)
    } catch (error) {
      console.error('خطأ في استخراج الألوان:', error)
      alert('حدث خطأ أثناء استخراج الألوان من الصورة')
    } finally {
      setIsExtracting(false)
    }
  }

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }, [handleFileSelect])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }, [])

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files
    if (files && files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleAddToFavorites = () => {
    if (currentPalette) {
      onAddToFavorites(currentPalette)
    }
  }

  return (
    <div className="space-y-6">
      {/* إعدادات الاستخراج */}
      <div className="card">
        <div className="card-header">
          <h2 className="card-title">استخراج الألوان من الصور</h2>
          <p className="card-description">
            ارفع صورة أو اسحبها هنا لاستخراج الألوان المهيمنة منها
          </p>
        </div>

        <div className="flex items-center gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-primary mb-2">
              عدد الألوان ({colorCount})
            </label>
            <input
              type="range"
              min="3"
              max="12"
              value={colorCount}
              onChange={(e) => setColorCount(parseInt(e.target.value))}
              className="w-32"
            />
          </div>
        </div>

        {/* منطقة السحب والإفلات */}
        <div
          className={`drop-zone ${dragOver ? 'drag-over' : ''}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => fileInputRef.current?.click()}
        >
          <div className="drop-zone-icon">
            {isExtracting ? (
              <Loader className="w-12 h-12 animate-spin" />
            ) : (
              <Upload className="w-12 h-12" />
            )}
          </div>
          
          <div className="drop-zone-text">
            {isExtracting ? 'جاري استخراج الألوان...' : 'اسحب صورة هنا أو انقر للاختيار'}
          </div>
          
          <div className="drop-zone-subtext">
            يدعم JPG, PNG, GIF, WebP (حتى 10MB)
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
        </div>
      </div>

      {/* عرض الصورة المحددة */}
      {selectedImage && (
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">الصورة المحددة</h3>
          </div>
          
          <div className="flex justify-center">
            <img
              src={selectedImage}
              alt="الصورة المحددة"
              className="max-w-full max-h-64 object-contain rounded-lg border border-border"
            />
          </div>
        </div>
      )}

      {/* عرض لوحة الألوان المستخرجة */}
      {currentPalette && currentPalette.type === 'extracted' && (
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="card-title">{currentPalette.name}</h2>
                <p className="card-description">
                  {currentPalette.colors.length} لون مستخرج من الصورة
                </p>
              </div>
              
              <div className="flex gap-2">
                <button
                  onClick={handleAddToFavorites}
                  className="btn btn-secondary"
                  title="إضافة للمفضلة"
                >
                  <Heart className="w-4 h-4" />
                  حفظ
                </button>
                
                <button
                  onClick={() => setShowExportModal(true)}
                  className="btn btn-secondary"
                  title="تصدير"
                >
                  <Download className="w-4 h-4" />
                  تصدير
                </button>
              </div>
            </div>
          </div>

          <ColorDisplay
            colors={currentPalette.colors}
            showCopyButtons={true}
          />
        </div>
      )}

      {/* رسالة ترحيبية */}
      {!selectedImage && !isExtracting && (
        <div className="card text-center py-12">
          <div className="w-16 h-16 bg-primary-color/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <ImageIcon className="w-8 h-8 text-primary-color" />
          </div>
          <h3 className="text-lg font-semibold text-primary mb-2">
            استخرج الألوان من صورك
          </h3>
          <p className="text-secondary mb-6 max-w-md mx-auto">
            ارفع أي صورة وسيقوم البرنامج بتحليلها واستخراج الألوان المهيمنة منها تلقائياً
          </p>
          <button
            onClick={() => fileInputRef.current?.click()}
            className="btn btn-primary"
          >
            <Upload className="w-4 h-4" />
            اختر صورة
          </button>
        </div>
      )}

      {/* نافذة التصدير */}
      {currentPalette && (
        <ExportModal
          palette={currentPalette}
          isOpen={showExportModal}
          onClose={() => setShowExportModal(false)}
        />
      )}
    </div>
  )
}

export default ColorExtractor
