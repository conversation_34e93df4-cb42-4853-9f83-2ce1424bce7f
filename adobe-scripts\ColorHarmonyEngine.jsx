/*
 * Color Harmony Engine - محرك التناغم اللوني
 * خوارزميات متقدمة لتوليد لوحات الألوان المتناسقة
 */

// خوارزميات التناغم اللوني المتقدمة
var ColorHarmonyEngine = {
    
    // توليد لوحة ألوان مكملة متقدمة
    generateComplementary: function(baseHsl, count) {
        var colors = [];
        var complementaryHue = (baseHsl.h + 180) % 360;
        
        // اللون الأساسي
        colors.push({
            h: baseHsl.h,
            s: baseHsl.s,
            l: baseHsl.l,
            name: "أساسي"
        });
        
        // اللون المكمل
        colors.push({
            h: complementaryHue,
            s: baseHsl.s,
            l: baseHsl.l,
            name: "مكمل"
        });
        
        // تدرجات متنوعة
        for (var i = 2; i < count; i++) {
            var isBaseFamily = (i % 2 === 0);
            var hue = isBaseFamily ? baseHsl.h : complementaryHue;
            
            // تنويع في التشبع والإضاءة
            var saturation = Math.max(20, Math.min(100, baseHsl.s + (Math.random() - 0.5) * 40));
            var lightness = Math.max(15, Math.min(85, baseHsl.l + (Math.random() - 0.5) * 50));
            
            colors.push({
                h: hue,
                s: saturation,
                l: lightness,
                name: isBaseFamily ? "تدرج أساسي " + Math.floor(i/2) : "تدرج مكمل " + Math.floor(i/2)
            });
        }
        
        return colors;
    },
    
    // توليد لوحة ألوان متجاورة
    generateAnalogous: function(baseHsl, count) {
        var colors = [];
        var angleStep = 30; // زاوية التباعد
        var startAngle = baseHsl.h - (angleStep * Math.floor(count/2));
        
        for (var i = 0; i < count; i++) {
            var hue = (startAngle + (i * angleStep)) % 360;
            if (hue < 0) hue += 360;
            
            // تنويع طفيف في التشبع والإضاءة
            var saturationVariation = (Math.random() - 0.5) * 20;
            var lightnessVariation = (Math.random() - 0.5) * 30;
            
            var saturation = Math.max(30, Math.min(100, baseHsl.s + saturationVariation));
            var lightness = Math.max(20, Math.min(80, baseHsl.l + lightnessVariation));
            
            colors.push({
                h: hue,
                s: saturation,
                l: lightness,
                name: i === Math.floor(count/2) ? "أساسي" : "متجاور " + (i + 1)
            });
        }
        
        return colors;
    },
    
    // توليد لوحة ألوان ثلاثية
    generateTriadic: function(baseHsl, count) {
        var colors = [];
        var triadicHues = [
            baseHsl.h,
            (baseHsl.h + 120) % 360,
            (baseHsl.h + 240) % 360
        ];
        
        for (var i = 0; i < count; i++) {
            var hueIndex = i % 3;
            var hue = triadicHues[hueIndex];
            
            // تنويع في التشبع والإضاءة حسب المجموعة
            var groupIndex = Math.floor(i / 3);
            var saturation = Math.max(25, Math.min(100, baseHsl.s - (groupIndex * 15)));
            var lightness = Math.max(15, Math.min(85, baseHsl.l + (groupIndex * 20) - 30));
            
            colors.push({
                h: hue,
                s: saturation,
                l: lightness,
                name: i < 3 ? "ثلاثي " + (i + 1) : "تدرج " + (i + 1)
            });
        }
        
        return colors;
    },
    
    // توليد لوحة ألوان رباعية
    generateTetradic: function(baseHsl, count) {
        var colors = [];
        var tetradicHues = [
            baseHsl.h,
            (baseHsl.h + 90) % 360,
            (baseHsl.h + 180) % 360,
            (baseHsl.h + 270) % 360
        ];
        
        for (var i = 0; i < count; i++) {
            var hueIndex = i % 4;
            var hue = tetradicHues[hueIndex];
            
            // تنويع متدرج
            var variation = Math.floor(i / 4);
            var saturation = Math.max(20, Math.min(100, baseHsl.s - (variation * 10)));
            var lightness = Math.max(10, Math.min(90, baseHsl.l + (variation * 15) - 20));
            
            colors.push({
                h: hue,
                s: saturation,
                l: lightness,
                name: i < 4 ? "رباعي " + (i + 1) : "تدرج " + (i + 1)
            });
        }
        
        return colors;
    },
    
    // توليد لوحة ألوان أحادية اللون
    generateMonochromatic: function(baseHsl, count) {
        var colors = [];
        var lightnessRange = 70; // نطاق الإضاءة
        var saturationRange = 40; // نطاق التشبع
        
        for (var i = 0; i < count; i++) {
            var factor = i / (count - 1); // من 0 إلى 1
            
            // توزيع الإضاءة من الداكن إلى الفاتح
            var lightness = 15 + (factor * lightnessRange);
            
            // تنويع طفيف في التشبع
            var saturationVariation = (Math.random() - 0.5) * saturationRange;
            var saturation = Math.max(10, Math.min(100, baseHsl.s + saturationVariation));
            
            colors.push({
                h: baseHsl.h,
                s: saturation,
                l: lightness,
                name: i === Math.floor(count/2) ? "أساسي" : "تدرج " + (i + 1)
            });
        }
        
        return colors;
    },
    
    // توليد لوحة ألوان مكمل منقسم
    generateSplitComplementary: function(baseHsl, count) {
        var colors = [];
        var complementaryHue = (baseHsl.h + 180) % 360;
        var splitAngle = 30;
        
        var splitHue1 = (complementaryHue - splitAngle + 360) % 360;
        var splitHue2 = (complementaryHue + splitAngle) % 360;
        
        var hues = [baseHsl.h, splitHue1, splitHue2];
        
        for (var i = 0; i < count; i++) {
            var hueIndex = i % 3;
            var hue = hues[hueIndex];
            
            // تنويع في التشبع والإضاءة
            var groupIndex = Math.floor(i / 3);
            var saturation = Math.max(25, Math.min(100, baseHsl.s - (groupIndex * 12)));
            var lightness = Math.max(15, Math.min(85, baseHsl.l + (groupIndex * 18) - 25));
            
            var name;
            if (i === 0) name = "أساسي";
            else if (hueIndex === 1) name = "مكمل منقسم 1";
            else if (hueIndex === 2) name = "مكمل منقسم 2";
            else name = "تدرج " + (i + 1);
            
            colors.push({
                h: hue,
                s: saturation,
                l: lightness,
                name: name
            });
        }
        
        return colors;
    },
    
    // توليد لوحة ألوان عشوائية متناسقة
    generateRandom: function(baseHsl, count) {
        var colors = [];
        var harmonies = ['complementary', 'analogous', 'triadic'];
        var randomHarmony = harmonies[Math.floor(Math.random() * harmonies.length)];
        
        switch (randomHarmony) {
            case 'complementary':
                return this.generateComplementary(baseHsl, count);
            case 'analogous':
                return this.generateAnalogous(baseHsl, count);
            case 'triadic':
                return this.generateTriadic(baseHsl, count);
            default:
                return this.generateComplementary(baseHsl, count);
        }
    },
    
    // تحسين لوحة الألوان (ضبط التباين والانسجام)
    optimizePalette: function(colors) {
        var optimized = [];
        
        for (var i = 0; i < colors.length; i++) {
            var color = colors[i];
            var optimizedColor = {
                h: color.h,
                s: Math.max(15, Math.min(95, color.s)), // ضبط التشبع
                l: Math.max(10, Math.min(90, color.l)), // ضبط الإضاءة
                name: color.name
            };
            
            // تجنب الألوان المتشابهة جداً
            var isDuplicate = false;
            for (var j = 0; j < optimized.length; j++) {
                var existing = optimized[j];
                var hueDiff = Math.abs(optimizedColor.h - existing.h);
                var satDiff = Math.abs(optimizedColor.s - existing.s);
                var lightDiff = Math.abs(optimizedColor.l - existing.l);
                
                if (hueDiff < 10 && satDiff < 15 && lightDiff < 15) {
                    isDuplicate = true;
                    break;
                }
            }
            
            if (!isDuplicate) {
                optimized.push(optimizedColor);
            }
        }
        
        return optimized;
    },
    
    // تحويل HSL إلى RGB
    hslToRgb: function(h, s, l) {
        h /= 360;
        s /= 100;
        l /= 100;
        
        var r, g, b;
        
        if (s === 0) {
            r = g = b = l;
        } else {
            var hue2rgb = function(p, q, t) {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1/6) return p + (q - p) * 6 * t;
                if (t < 1/2) return q;
                if (t < 2/3) return p + (q - p) * (2/3 - t) * 6;
                return p;
            };
            
            var q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            var p = 2 * l - q;
            r = hue2rgb(p, q, h + 1/3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1/3);
        }
        
        return {
            r: Math.round(r * 255),
            g: Math.round(g * 255),
            b: Math.round(b * 255)
        };
    }
};
