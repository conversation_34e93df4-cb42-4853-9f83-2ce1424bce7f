import { contextBridge, ipcRenderer } from 'electron'

// تعريف API للتطبيق
const electronAPI = {
  // حفظ وتحميل الملفات
  saveFile: (data: string, defaultPath?: string) => 
    ipcRenderer.invoke('save-file', data, defaultPath),
  
  loadFile: () => 
    ipcRenderer.invoke('load-file'),
  
  openImage: () => 
    ipcRenderer.invoke('open-image'),

  // الاستماع لأحداث القائمة
  onMenuOpenImage: (callback: () => void) => 
    ipcRenderer.on('menu-open-image', callback),
  
  onMenuSavePalette: (callback: () => void) => 
    ipcRenderer.on('menu-save-palette', callback),

  // إزالة المستمعين
  removeAllListeners: (channel: string) => 
    ipcRenderer.removeAllListeners(channel)
}

// تصدير API للنافذة
contextBridge.exposeInMainWorld('electronAPI', electronAPI)

// تعريف النوع للـ TypeScript
export type ElectronAPI = typeof electronAPI
