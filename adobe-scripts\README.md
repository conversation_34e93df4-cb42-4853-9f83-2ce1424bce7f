# مولد لوحات الألوان لبرامج Adobe - Color Palette Generator

سكريبت JSX احترافي لتوليد وإدارة لوحات الألوان داخل Adobe Photoshop و Illustrator.

## 🎨 المميزات

### ✅ الوظائف الأساسية:
- **توليد لوحات ألوان تلقائيًا** - 6 أنواع من نظريات الألوان
- **استخراج الألوان من الصور** - تحليل الصور واستخراج الألوان المهيمنة (Photoshop)
- **رسم مربعات الألوان** - إدراج الألوان مباشرة في المشروع
- **إضافة إلى Swatches** - حفظ الألوان في لوحة الألوان
- **تصدير متعدد الصيغ** - .ase, .txt, .json
- **واجهة سهلة الاستخدام** - تصميم بديهي باللغة العربية

### 🎯 نظريات الألوان المدعومة:
1. **مكمل (Complementary)** - ألوان متقابلة في عجلة الألوان
2. **متجاور (Analogous)** - ألوان متجاورة في عجلة الألوان  
3. **ثلاثي (Triadic)** - ثلاثة ألوان متباعدة بالتساوي
4. **رباعي (Tetradic)** - أربعة ألوان تشكل مستطيل
5. **أحادي اللون (Monochromatic)** - تدرجات من نفس اللون
6. **مكمل منقسم (Split Complementary)** - لون أساسي مع لونين مجاورين لمكمله

## 📋 متطلبات النظام

### البرامج المدعومة:
- **Adobe Photoshop CS6** أو أحدث
- **Adobe Illustrator CC** أو أحدث

### متطلبات إضافية:
- نظام التشغيل: Windows 7+ أو macOS 10.9+
- ذاكرة: 4GB RAM أو أكثر
- مساحة القرص: 50MB مساحة فارغة

## 🚀 التثبيت

### الطريقة 1: النسخ المباشر
1. حمل ملفات السكريبت من المجلد `adobe-scripts/`
2. انسخ الملفات إلى مجلد Scripts في Adobe:
   - **Windows**: `C:\Program Files\Adobe\[Program Name]\Presets\Scripts\`
   - **macOS**: `/Applications/Adobe [Program Name]/Presets/Scripts/`

### الطريقة 2: التشغيل المباشر
1. افتح Photoshop أو Illustrator
2. اذهب إلى `File > Scripts > Browse...`
3. اختر ملف `ColorPaletteGenerator.jsx`

### الطريقة 3: إنشاء اختصار
1. ضع الملفات في مجلد Scripts
2. أعد تشغيل البرنامج
3. ستجد السكريبت في `File > Scripts > ColorPaletteGenerator`

## 📖 دليل الاستخدام

### البدء السريع:
1. افتح مستند جديد أو موجود
2. شغل السكريبت من `File > Scripts`
3. اختر مصدر الألوان (توليد أو استخراج)
4. اضبط الإعدادات حسب الحاجة
5. اضغط "توليد لوحة الألوان"

### توليد لوحة من لون أساسي:
1. اختر "توليد من لون أساسي"
2. أدخل كود اللون (مثل: #6366f1)
3. اختر نوع التناغم المطلوب
4. حدد عدد الألوان (3-8)
5. فعل الخيارات المطلوبة:
   - ☑️ رسم مربعات الألوان
   - ☑️ إضافة إلى Swatches
   - ☑️ تصدير كملف

### استخراج الألوان من صورة (Photoshop فقط):
1. افتح صورة في Photoshop
2. حدد الطبقة المطلوبة
3. اختر "استخراج من صورة محددة"
4. اختر طريقة الاستخراج:
   - **تحليل متقدم**: أبطأ لكن أدق
   - **تحليل سريع**: أسرع لكن تقريبي
5. حدد عدد الألوان المطلوب استخراجها

## 🎮 الواجهة والتحكم

### النافذة الرئيسية:
- **مصدر الألوان**: اختيار بين التوليد والاستخراج
- **إعدادات التوليد**: اللون الأساسي، نوع التناغم، عدد الألوان
- **الإجراءات**: خيارات الرسم والحفظ والتصدير

### الاختصارات والنصائح:
- استخدم أكواد HEX صحيحة (مثل: #FF5733)
- جرب أنواع التناغم المختلفة للحصول على نتائج متنوعة
- ابدأ بعدد قليل من الألوان (4-5) ثم زد حسب الحاجة
- احفظ لوحاتك المفضلة كملفات للاستخدام المستقبلي

## 📁 ملفات السكريبت

```
adobe-scripts/
├── ColorPaletteGenerator.jsx    # السكريبت الرئيسي
├── ColorHarmonyEngine.jsx       # محرك نظريات الألوان
├── ImageColorExtractor.jsx      # مستخرج الألوان من الصور
├── CompatibilityTest.jsx        # سكريبت اختبار التوافق
└── README.md                    # هذا الدليل
```

### وصف الملفات:
- **ColorPaletteGenerator.jsx**: الملف الرئيسي الذي يحتوي على جميع الوظائف
- **ColorHarmonyEngine.jsx**: خوارزميات متقدمة لنظريات الألوان
- **ImageColorExtractor.jsx**: وظائف استخراج الألوان من الصور
- **CompatibilityTest.jsx**: اختبار شامل لجميع الوظائف

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها:

#### "لا يمكن تشغيل السكريبت"
**الأسباب المحتملة:**
- إصدار البرنامج قديم
- ملف السكريبت تالف
- مشاكل في الصلاحيات

**الحلول:**
1. تأكد من استخدام إصدار مدعوم
2. أعد تحميل ملف السكريبت
3. شغل البرنامج كمدير (Administrator)

#### "لا يوجد مستند مفتوح"
**الحل:** افتح مستند جديد أو موجود قبل تشغيل السكريبت

#### "فشل في رسم الألوان"
**الأسباب المحتملة:**
- مساحة العمل ممتلئة
- مشاكل في الذاكرة

**الحلول:**
1. احفظ عملك وأعد تشغيل البرنامج
2. قلل عدد الألوان المطلوبة
3. أغلق المستندات غير المستخدمة

#### "استخراج الألوان لا يعمل"
**ملاحظة:** هذه الوظيفة متاحة فقط في Photoshop

**الحلول:**
1. تأكد من تحديد طبقة صورة صالحة
2. جرب الطريقة المبسطة إذا فشلت المتقدمة
3. تأكد من أن الصورة تحتوي على ألوان متنوعة

### رسائل الخطأ الشائعة:

| رسالة الخطأ | السبب | الحل |
|------------|--------|------|
| "يرجى تحديد طبقة صورة صالحة" | الطبقة المحددة ليست صورة | حدد طبقة تحتوي على صورة |
| "كود اللون غير صحيح" | تنسيق HEX خاطئ | استخدم تنسيق #RRGGBB |
| "فشل في إنشاء الملف" | مشاكل في الصلاحيات | اختر مجلد آخر للحفظ |

## 🎯 أمثلة عملية

### مثال 1: لوحة ألوان لموقع ويب
1. ابدأ باللون الأساسي: `#3498db` (أزرق)
2. اختر نوع التناغم: "مكمل"
3. عدد الألوان: 5
4. النتيجة: لوحة متوازنة للويب

### مثال 2: استخراج ألوان من شعار
1. افتح صورة الشعار في Photoshop
2. حدد طبقة الشعار
3. استخدم "استخراج من صورة"
4. اختر 4-6 ألوان
5. احفظ النتيجة كـ .ase للاستخدام في التصميم

### مثال 3: لوحة ألوان طبيعية
1. اللون الأساسي: `#27ae60` (أخضر طبيعي)
2. نوع التناغم: "متجاور"
3. عدد الألوان: 6
4. النتيجة: لوحة مستوحاة من الطبيعة

## 📞 الدعم والمساعدة

### للحصول على المساعدة:
1. راجع قسم "استكشاف الأخطاء" أولاً
2. جرب سكريبت اختبار التوافق
3. تأكد من استخدام إصدار مدعوم من البرنامج

### تقديم الاقتراحات:
نرحب بجميع الاقتراحات لتحسين السكريبت وإضافة مميزات جديدة.

## 📄 الترخيص

هذا السكريبت مجاني للاستخدام الشخصي والتجاري. يُمنع إعادة التوزيع بدون إذن.

---

**تم تطويره بـ ❤️ للمصممين العرب**

*آخر تحديث: ديسمبر 2024*
